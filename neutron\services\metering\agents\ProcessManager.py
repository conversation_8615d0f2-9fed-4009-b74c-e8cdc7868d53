#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import ConfigParser
import datetime
import socket
import time

from oslo_config import cfg
from oslo_log import log as logging
from oslo_serialization import jsonutils
from oslo_utils import timeutils
from pykafka import exceptions as kafka_exc

from neutron.agent.linux import ip_lib
from neutron.services.metering.agents import kafka_manager as kafka_mgt
from neutron.services.metering.collection import collect_router
from neutron.services.metering.common import constants as meter_const
from neutron.services.metering.common import log_derorator
from neutron.services.metering.common import processloopingcall as pl
from neutron.services.metering.common import processmanager as pm


VPC_COUNTER_LOG = 'vpc_counter.log'
ROUTER_CON_COUNTER_LOG = 'router_connections_counter.log'
WARNING_TIME_THREASHOLD = 300

LOG = logging.getLogger(__name__)
conf = cfg.CONF

hostname = socket.gethostname()
vpc_cnt_log = log_derorator.MonitorLogger(VPC_COUNTER_LOG)
router_con_cnt_log = log_derorator.MonitorLogger(ROUTER_CON_COUNTER_LOG)

mconf = ConfigParser.ConfigParser()
mconf.read("/etc/neutron/metering_agent.ini")
try:
    enable_conn_tcp = mconf.get("DEFAULT", "enable_conn_tcp")
    enable_conn_udp = mconf.get("DEFAULT", "enable_conn_udp")
    enable_conn_icmp = mconf.get("DEFAULT", "enable_conn_icmp")
except Exception as e:
    LOG.warning('Load conntrack tcp/udp/icmp switch failed.')


def _get_all_routers():
    router_ns = []
    total_numer = 0

    try:
        now_time1 = datetime.datetime.now()

        namespaces = ip_lib.list_network_namespaces()
        for ns in namespaces:
            if ns.startswith('qrouter'):
                if len(ns) >= meter_const.VROUTER_NS_LEN:
                    qrouter_ns = ns.split(' ')[0]
                    router_ns.append(qrouter_ns)
                    total_numer += 1

        now_time2 = datetime.datetime.now()
        LOG.debug('\n Foreach router %s namespace Eslaped:%s',
                  total_numer, now_time2 - now_time1)

    except Exception:
        LOG.error('Foreach router namespace failed.')

    return router_ns


def _send_to_kafka_and_writefile(kafka, producer_key,
                                 report_str):
    if producer_key not in kafka.kafka_dict:
        LOG.error("Kafka diction dost not contain %v", producer_key)
        return
    try:
        kafka.kafka_dict[producer_key].produce(bytes(report_str))
        LOG.debug("Send data:%s by %s %s", report_str, kafka, producer_key)
    except Exception as e:
        LOG.error("_send_to_kafka_and_writefile Kafka produce error: %s", e)


def _get_value_from_key(strs):
    rlist = []
    for k in strs:
        v = k.split('=')[1]
        rlist.append(v)
    return (int(rlist[0]), int(rlist[1]), int(rlist[2]), int(rlist[3]))


def _handle_conn_counter(ip_wrapper, cmd):
    conn = ip_wrapper.netns.execute(cmd, check_exit_code=False)
    output = conn.split('\n')
    output = output[1].split(' ')
    return _get_value_from_key(output)


def _get_router_connections_by_pro(ns_id):
    tcp = 0
    est = 0
    udp = 0
    icmp = 0

    try:
        cmd = ['neutron-conntrack-check']

        time_start = time.time()
        ip_wrapper = ip_lib.IPWrapper(namespace=ns_id)
        tcp, est, udp, icmp = _handle_conn_counter(ip_wrapper, cmd)

        time_end = time.time()
        LOG.debug(
            'Conntrack detail %s %s %s %s, escaped time=%s',
            tcp, est, udp, icmp, time_end - time_start)

    except Exception as e:
        LOG.error('reporting router connections details failed...'
                  'reason %(except)s', {'except': e})

    return (tcp, est, udp, icmp)


def monitor_router_conntrack_detail(name):
    LOG.info('Router conntrack detail loop '
             'conf.enable_monitor_router=%s',
             conf.enable_monitor_router)

    conn_types = [enable_conn_tcp, enable_conn_udp, enable_conn_icmp]
    if all(t.lower() == "false" for t in conn_types):
        LOG.debug("Router conntrack detail switch is closed.")
        return

    # connect kafka
    begin = time.time()
    collect_vpc = collect_router.MonitorRouter()
    kafmgr = kafka_mgt.KafkaManagerExt()
    kafmgr.reconnect_kafa()
    end = time.time()
    LOG.info(
        'Router conntrack detail kafmgr=%s, kafmgr.kafka_dict=%s escaped:%s',
        kafmgr,
        kafmgr.kafka_dict,
        end - begin)

    try:
        tbegin = time.time()
        LOG.info('Router conntrack detail task starting')
        routers = _get_all_routers()
        for q_ns in routers:
            tcp, est, udp, icmp = _get_router_connections_by_pro(q_ns)
            router_uuid = q_ns[meter_const.ROUTER_HEAD_LEN:]
            router_dict = {'timestamp': timeutils.utcnow_ts(),
                           'uuid': router_uuid,
                           'hostname': hostname}

            router_dict['connections_tcp'] = tcp
            router_dict['connections_est'] = est
            router_dict['connections_udp'] = udp
            router_dict['connections_icmp'] = icmp
            ns = 'qrouter-' + router_uuid
            ha_dev_name = collect_vpc.get_ha_interface_in_namespace(ns)
            router_dict['tag'] = collect_vpc.router_is_master_or_slave(
                ha_dev_name, ns)
            router_con_body_str = {'topic_router_con_detail': router_dict}
            report_str = jsonutils.dumps(
                router_con_body_str, ensure_ascii=False, indent=1)

            LOG.debug('Reporting Router conntrack detail: \n %s', report_str)

            _send_to_kafka_and_writefile(
                kafmgr, 'producer_monitor_topic_router_con_detail', report_str)

            kafmgr.router_conn_detail_counter += 1

        LOG.info('Router conntrack detail counter=%s',
                 kafmgr.router_conn_detail_counter)
        LOG.info('Router conntrack detail exit, escaped:%s',
                 time.time() - tbegin)
        if time.time() - tbegin > WARNING_TIME_THREASHOLD:
            LOG.warning('Router conntrack detail cost time=%s',
                        time.time() - tbegin)

    except kafka_exc.NoBrokersAvailableError:
        LOG.warning(
            'Re-init Kafka client in router loop')

    except (kafka_exc.SocketDisconnectedError,
            kafka_exc.LeaderNotAvailable) as ex:
        LOG.exception("Kafka connection has lost, "
                      "reconnect and resending...ex=%s ", ex)

    except Exception as e:
        LOG.exception('Send failed in router conn loop, e=%s ', e)


def _get_vpc_namespace():
    router_ns = set()
    try:
        namespaces = ip_lib.list_network_namespaces()
        for ns in namespaces:
            if ns.startswith('qrouter'):
                router_ns.add(ns)

        if len(router_ns) == 0:
            LOG.warning('collecting VPC but not found any router '
                        'namespace...')
            return set()
    except Exception as e:
        LOG.error('analying VPC namespace failed...%(router_ns)s reason'
                  ' %(except)s', {'router_ns': router_ns, 'except': e})
        return set()

    return router_ns


def _build_external_dev_info(ns_uuid, ip_wrapper, ip_devs, collect_vpc):
    devs = {}
    for ip_dev in ip_devs:
        if ip_dev.name.startswith('qg'):
            data = {'name': ip_dev.name, 'inter_pkts': 0,
                    'inter_bytes': 0, 'ext_pkts': 0,
                    'ext_bytes': 0, 'tx_drop': 0, 'rx_drop': 0}
            ifcmd = ['ifconfig', ip_dev.name]
            output = ip_wrapper.netns.execute(
                ifcmd, run_as_root=True,
                check_exit_code=False)
            for line in output.split('\n'):
                ls = line.strip()
                if ls.startswith('RX packets'):
                    parts = ls.split(' ')
                    data['inter_pkts'] = int(parts[2])
                    data['inter_bytes'] = int(parts[5])
                elif ls.startswith('TX packets'):
                    parts = ls.split(' ')
                    data['ext_pkts'] = int(parts[2])
                    data['ext_bytes'] = int(parts[5])
                elif ls.startswith('RX errors'):
                    parts = ls.split(' ')
                    data['rx_drop'] = int(parts[5])
                elif ls.startswith('TX errors'):
                    parts = ls.split(' ')
                    data['tx_drop'] = int(parts[5])
            devs['qg_dev'] = data
            devs['timestamp'] = timeutils.utcnow_ts(
                microsecond=True)
            devs['router_uuid'] = ns_uuid
            devs['hostname'] = hostname
            ns = 'qrouter-' + ns_uuid
            ha_dev_name = collect_vpc.get_ha_interface_in_namespace(ns)
            devs['tag'] = collect_vpc.router_is_master_or_slave(
                ha_dev_name, ns)
            return devs
    return devs


def _monitor_vpc_counter(router_ns, kafmgr, collect_vpc):
    for ns in router_ns:
        if len(ns) >= meter_const.VROUTER_NS_LEN and \
                meter_const.ROUTER_LABLE in ns:
            ns = ns.split(' ')[0]
            ns_uuid = ns[(meter_const.ROUTER_LABLE_LEN + 1):]
            try:
                ip_wrapper = ip_lib.IPWrapper(namespace=ns)
                ip_devs = ip_wrapper.get_devices()
                devs = _build_external_dev_info(ns_uuid, ip_wrapper,
                                                ip_devs, collect_vpc)
                kafmgr.vpc_counter += 1

                if not devs:
                    # Do not report to kafka without external traffic
                    LOG.debug('qg dev does not exist in ns_uuid=%s', ns_uuid)
                    continue

            except Exception as e:
                LOG.exception('Analying Vpc namespace failed...%s, reason:%s',
                          ns, e)
                continue

            vpc_counter = {'topic_vpc': devs}
            report_str = jsonutils.dumps(
                vpc_counter, ensure_ascii=False, indent=1)
            LOG.info("Reporting vpc counter =%s", report_str)
            _send_to_kafka_and_writefile(
                kafmgr, 'producer_monitor_topic_vpc', report_str)
            kafmgr.vpc_qg_counter += 1

    LOG.info('Vpc counter=%s, vpc_qg_counter=%s', kafmgr.vpc_counter,
             kafmgr.vpc_qg_counter)


def collect_vpc_loop(name):
    if conf.enable_monitor_vpc:
        LOG.info('Vpc loop task starting enable_monitor_vpc=%s ',
                 conf.enable_monitor_vpc)
        begin = time.time()
        collect_vpc = collect_router.MonitorRouter()
        kafmgrVpc = kafka_mgt.KafkaManagerExt()
        kafmgrVpc.reconnect_kafa()
        end = time.time()
        LOG.info('Vpc kafmgr=%s, kafmgr.kafka_dict=%s escaped:%s',
                 kafmgrVpc,
                 kafmgrVpc.kafka_dict,
                 end - begin)

        router_ns = _get_vpc_namespace()

        try:
            _monitor_vpc_counter(router_ns, kafmgrVpc, collect_vpc)

            now_time_end = time.time()
            LOG.info('Vpc counter task exit, escaped=%s', now_time_end - begin)
            if now_time_end - begin > conf.report_interval:
                LOG.warning('Vpc statistics cost time=%s > %s',
                            now_time_end - begin, conf.report_interval)

        except kafka_exc.NoBrokersAvailableError:
            LOG.warning('Reinit Kafka client in Vpc')

        except (kafka_exc.SocketDisconnectedError,
                kafka_exc.LeaderNotAvailable) as ex:
            LOG.exception("Kafka connection has lost, "
                          "Reconnect in Vpc loop, ex=%s ", ex)
        except Exception as e:
            LOG.exception('Send failed in Vpc loop, e=%s', e)


class ProcessManager(object):
    def __init__(self):
        self.router_conntrack_detail = pl.FixedIntervalLoopingCall(
            monitor_router_conntrack_detail, 'conntrack-detail')
        # tcp udp icmp same interval
        self.router_conntrack_detail.start(
            interval=conf.report_router_tcp_con_interval)

        self.vpc_counter = pl.FixedIntervalLoopingCall(
            collect_vpc_loop, 'vpc')
        self.vpc_counter.start(interval=conf.report_interval)

    def monitor_start(self):
        self.processsupervisor = pm.ProcessSupervisor(60)
        self.processsupervisor.register('router_conntrack_detail',
                                        self.router_conntrack_detail.jobs[0],
                                        conf.report_router_tcp_con_interval)
        self.processsupervisor.register('vpc_counter',
                                        self.vpc_counter.jobs[0],
                                        conf.report_interval)

    def __del__(self):
        self.router_conntrack_detail.stop()
        self.vpc_counter.stop()
