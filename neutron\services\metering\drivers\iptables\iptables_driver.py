# <AUTHOR> <EMAIL>
#
# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.
import os
import time

import netaddr
from oslo_config import cfg
from oslo_log import helpers as log_helpers
from oslo_log import log as logging
from oslo_utils import excutils
from oslo_utils import fileutils

from neutron.agent.common import utils as common_utils
from neutron.agent.l3 import dvr_snat_ns
from neutron.agent.l3 import namespaces
from neutron.agent.linux import ip_lib
from neutron.agent.linux import ipset_manager
from neutron.agent.linux import iptables_manager
from neutron.agent.linux import utils as linux_utils
from neutron.common import constants
from neutron.common import ipv6_utils
from neutron.conf.agent import common as config
from neutron.services.metering.drivers import abstract_driver


LOG = logging.getLogger(__name__)
NS_PREFIX = 'qrouter-'
WRAP_NAME = 'neutron-meter'
EXTERNAL_DEV_PREFIX = 'qg-'
ROUTER_2_FIP_DEV_PREFIX = namespaces.ROUTER_2_FIP_DEV_PREFIX
TOP_CHAIN = WRAP_NAME + "-FORWARD"
RULE = '-r-'
EIP_RULE = RULE + 'e-'
PF_RULE = RULE + 'p-'
SNAT_RULE = RULE + 's-'
LABEL = '-l-'

config.register_interface_driver_opts_helper(cfg.CONF)
config.register_interface_opts()


class IptablesManagerTransaction(object):
    __transactions = {}

    def __init__(self, im):
        self.im = im

        transaction = self.__transactions.get(im, 0)
        transaction += 1
        self.__transactions[im] = transaction

    def __enter__(self):
        return self.im

    def __exit__(self, type, value, traceback):
        transaction = self.__transactions.get(self.im)
        if transaction == 1:
            self.im.apply()
            del self.__transactions[self.im]
        else:
            transaction -= 1
            self.__transactions[self.im] = transaction


class RouterWithMetering(object):

    def __init__(self, conf, router):
        self.conf = conf
        self.id = router['id']
        self.router = router
        # TODO(cbrandily): deduplicate ns_name generation in metering/l3
        self.ns_name = NS_PREFIX + self.id
        self.iptables_manager = None
        self.snat_iptables_manager = None
        self.ipsets_manager = None
        self.snat_ipsets_manager = None
        self.metering_labels = {}

        self.create_iptables_managers()
        self.create_ipsets_managers()

    def create_iptables_managers(self):
        """Creates iptables managers if the are not already created

        Returns True if any manager is created
        """

        created = False

        if self.router['distributed'] and self.snat_iptables_manager is None:
            # If distributed routers then we need to apply the
            # metering agent label rules in the snat namespace as well.
            snat_ns_name = dvr_snat_ns.SnatNamespace.get_snat_ns_name(
                self.id)
            # Check for namespace existence before we assign the
            # snat_iptables_manager
            if ip_lib.network_namespace_exists(snat_ns_name):
                self.snat_iptables_manager = iptables_manager.IptablesManager(
                    namespace=snat_ns_name,
                    binary_name=WRAP_NAME,
                    state_less=True,
                    use_ipv6=ipv6_utils.is_enabled_and_bind_by_default())

                created = True

        if self.iptables_manager is None:
            # Check of namespace existence before we assign the
            # iptables_manager
            # NOTE(Swami): If distributed routers, all external traffic on a
            # compute node will flow through the rfp interface in the router
            # namespace.
            if ip_lib.network_namespace_exists(self.ns_name):
                self.iptables_manager = iptables_manager.IptablesManager(
                    namespace=self.ns_name,
                    binary_name=WRAP_NAME,
                    state_less=True,
                    use_ipv6=ipv6_utils.is_enabled_and_bind_by_default())

                created = True

        return created

    def create_ipsets_managers(self):
        """Creates ipsets managers if the are not already created

        Returns True if any manager is created
        """

        created = False

        if self.router['distributed'] and self.snat_ipsets_manager is None:
            # If distributed routers then we need to apply the
            # metering agent label rules in the snat namespace as well.
            snat_ns_name = dvr_snat_ns.SnatNamespace.get_snat_ns_name(
                self.id)
            # Check for namespace existence before we assign the
            # snat_iptables_manager
            if ip_lib.network_namespace_exists(snat_ns_name):
                self.snat_ipsets_manager = ipset_manager.IpsetManager(
                    namespace=snat_ns_name, load_ipset=True)

                created = True

        if self.ipsets_manager is None:
            # Check of namespace existence before we assign the
            # iptables_manager
            # NOTE(Swami): If distributed routers, all external traffic on a
            # compute node will flow through the rfp interface in the router
            # namespace.
            if ip_lib.network_namespace_exists(self.ns_name):
                self.ipsets_manager = ipset_manager.IpsetManager(
                    namespace=self.ns_name, load_ipset=True)

                created = True

        return created


class IptablesMeteringDriver(abstract_driver.MeteringAbstractDriver):

    def __init__(self, plugin, conf):
        self.plugin = plugin
        self.conf = conf or cfg.CONF
        self.routers = {}

        self.driver = common_utils.load_interface_driver(self.conf)

    def _update_router(self, router):
        r = self.routers.get(router['id'])

        if r is None:
            r = RouterWithMetering(self.conf, router)

        r.router = router
        self.routers[r.id] = r

        return r

    @log_helpers.log_method_call
    def update_routers(self, context, routers):
        # disassociate removed routers
        router_ids = set(router['id'] for router in routers)
        for router_id, rm in self.routers.items():
            if router_id not in router_ids:
                self._process_disassociate_metering_label(rm.router)

        for router in routers:
            old_gw_port_id = None
            old_address_groups = []
            old_rm = self.routers.get(router['id'])
            old_rm_exist = False
            if old_rm:
                old_rm_exist = True
                old_gw_port_id = old_rm.router['gw_port_id']
                old_address_groups = old_rm.router.get(
                    constants.ADDRESS_GROUP_KEY, [])
            gw_port_id = router['gw_port_id']

            self._update_router(router)

            new_address_groups = router.get(constants.ADDRESS_GROUP_KEY, [])
            if new_address_groups != old_address_groups:
                self._process_address_group(router['id'])

            if gw_port_id != old_gw_port_id:
                if old_rm_exist:
                    self._process_disassociate_metering_label(router)
                    if gw_port_id:
                        self._process_associate_metering_label(router)
                elif gw_port_id:
                    self._process_associate_metering_label(router)

            self._remove_address_group(router)

    @log_helpers.log_method_call
    def remove_router(self, context, router_id):
        if router_id in self.routers:
            del self.routers[router_id]

    def get_external_device_names(self, rm):
        gw_port_id = rm.router.get('gw_port_id')
        if not gw_port_id:
            return None, None

        # NOTE (Swami): External device 'qg' should be used on the
        # Router namespace if the router is legacy and should be used on
        # SNAT namespace if the router is distributed.
        qg_dev = (EXTERNAL_DEV_PREFIX +
                  gw_port_id)[:self.driver.DEV_NAME_LEN]
        rfp_dev = (ROUTER_2_FIP_DEV_PREFIX +
                   rm.id)[:self.driver.DEV_NAME_LEN]
        if rm.router.get('distributed'):
            return rfp_dev, qg_dev
        else:
            return qg_dev, rfp_dev

    def _process_metering_label_rules(self, rules, label_chain,
                                      rules_chain, ext_dev, im, iperversion):
        if not ext_dev:
            return
        for rule in rules:
            self._add_rule_to_chain(ext_dev, rule, im,
                                    label_chain, rules_chain, iperversion)

    def _add_rule_to_chain(self, ext_dev, rule, im,
                           label_chain, rules_chain, iperversion):
        ipt_rule = self._prepare_rule(ext_dev, rule, label_chain)
        if rule['excluded']:
            if iperversion == 'ipv6':
                im.ipv6['filter'].add_rule(rules_chain, ipt_rule,
                                           wrap=False, top=True)
            else:
                im.ipv4['filter'].add_rule(rules_chain, ipt_rule,
                                           wrap=False, top=True)
        else:
            if iperversion == 'ipv6':
                im.ipv6['filter'].add_rule(rules_chain, ipt_rule,
                                           wrap=False, top=False)
            else:
                im.ipv4['filter'].add_rule(rules_chain, ipt_rule,
                                           wrap=False, top=False)

    def _remove_rule_from_chain(self, ext_dev, rule, im,
                                label_chain, rules_chain, iperversion):
        ipt_rule = self._prepare_rule(ext_dev, rule, label_chain)
        if rule['excluded']:
            if iperversion == 'ipv6':
                im.ipv6['filter'].remove_rule(rules_chain, ipt_rule,
                                              wrap=False, top=True)
            else:
                im.ipv4['filter'].remove_rule(rules_chain, ipt_rule,
                                              wrap=False, top=True)
        else:
            if iperversion == 'ipv6':
                im.ipv6['filter'].remove_rule(rules_chain, ipt_rule,
                                              wrap=False, top=False)
            else:
                im.ipv4['filter'].remove_rule(rules_chain, ipt_rule,
                                              wrap=False, top=False)

    def _prepare_rule(self, ext_dev, rule, label_chain):
        protocol = ''
        port_num = ''
        if ';' in rule['remote_ip_prefix']:
            remote_ip = rule['remote_ip_prefix'].split(';')[0]
            protocol = rule['remote_ip_prefix'].split(';')[1]
            port_num = rule['remote_ip_prefix'].split(';')[2]
        else:
            remote_ip = rule['remote_ip_prefix']
        remote_ip = netaddr.IPNetwork(remote_ip).cidr
        if rule['direction'] == 'egress':
            if protocol and port_num:
                dir_opt = '-s %s -o %s -p %s --sport %s' % (
                    remote_ip, ext_dev, protocol, port_num)
            else:
                dir_opt = '-s %s -o %s' % (remote_ip, ext_dev)
            if rule.get('address_group_id'):
                ipset_name = self.get_ipset_name(rule.get('address_group_id'))
                ipset_opt = '-m set --match-set %s dst' % ipset_name
                dir_opt = '%s %s' % (dir_opt, ipset_opt)
        else:
            if protocol and port_num:
                dir_opt = '-d %s -i %s -p %s --dport %s' % (
                    remote_ip, ext_dev, protocol, port_num)
            else:
                dir_opt = '-d %s -i %s' % (remote_ip, ext_dev)
            if rule.get('address_group_id'):
                ipset_name = self.get_ipset_name(rule.get('address_group_id'))
                ipset_opt = '-m set --match-set %s src' % ipset_name
                dir_opt = '%s %s' % (dir_opt, ipset_opt)

        if rule['excluded']:
            ipt_rule = '%s -j RETURN' % dir_opt
        else:
            ipt_rule = '%s -j %s' % (dir_opt, label_chain)
        return ipt_rule

    def _from_chain_name(self, label, rule=False, multi=False):
        if not label:
            return
        if not rule:
            # Get label chain name: neutron-meter-l-xxxx
            chain = WRAP_NAME + LABEL + label['id']
            chain_name = iptables_manager.get_chain_name(chain, wrap=False)
            return chain_name
        else:
            # Get label chain name: neutron-meter-r-x-xxxx
            if not multi:
                chain = ''
                if label['type'] == constants.METERING_TYPE_EIP:
                    chain = WRAP_NAME + EIP_RULE + label['id']
                elif label['type'] == constants.METERING_TYPE_PF_EIP:
                    chain = WRAP_NAME + PF_RULE + label['id']
                elif label['type'] == constants.METERING_TYPE_SNAT_EIP:
                    chain = WRAP_NAME + SNAT_RULE + label['id']
                elif label['type'] == constants.METERING_TYPE_ECS_EIP:
                    chain = WRAP_NAME + RULE + label['id']
                chain_name = iptables_manager.get_chain_name(chain, wrap=False)
                return chain_name
            else:
                pf = WRAP_NAME + PF_RULE + label['id']
                chain_p = iptables_manager.get_chain_name(pf, wrap=False)
                snat = WRAP_NAME + SNAT_RULE + label['id']
                chain_s = iptables_manager.get_chain_name(snat, wrap=False)
                return chain_p, chain_s

    def _chain_exist(self, im, ipversion, chain):
        exist = False
        if ipversion == 'ipv6':
            if chain in im.ipv6['filter'].unwrapped_chains:
                exist = True
        else:
            if chain in im.ipv4['filter'].unwrapped_chains:
                exist = True
        return exist

    def _add_label_chain(self, im, label_chain, ipversion):
        if ipversion == 'ipv6':
            im.ipv6['filter'].add_chain(label_chain, wrap=False)
            im.ipv6['filter'].add_rule(label_chain, '', wrap=False)
        else:
            im.ipv4['filter'].add_chain(label_chain, wrap=False)
            im.ipv4['filter'].add_rule(label_chain, '-j ACCEPT', wrap=False)

    def _add_rule_chain(self, im, rule_chain, ipversion, is_snat=False):
        if ipversion == 'ipv6':
            im.ipv6['filter'].add_chain(rule_chain, wrap=False)
            im.ipv6['filter'].add_rule(TOP_CHAIN, '-j ' + rule_chain,
                                       wrap=False)
        else:
            top = False if is_snat else True
            im.ipv4['filter'].add_chain(rule_chain, wrap=False)
            im.ipv4['filter'].add_rule(TOP_CHAIN, '-j ' + rule_chain, top=top,
                                       wrap=False)

    def _separate_rules(self, label, rules):
        separate_rules = {constants.METERING_TYPE_PF_EIP: [],
                          constants.METERING_TYPE_SNAT_EIP: [],
                          constants.METERING_TYPE_EIP: []}
        if label['type'] != constants.METERING_TYPE_MULTI_EIP:
            return separate_rules
        else:
            for rule in rules:
                if rule['type'] not in separate_rules:
                    separate_rules[rule['type']] = []
                separate_rules[rule['type']].append(rule)
            return separate_rules

    def _check_or_add_multi_chain(self, im, ipversion, chain, is_snat=False):
        exist = self._chain_exist(im, ipversion, chain)
        if not exist:
            self._add_rule_chain(im, chain, ipversion, is_snat=is_snat)

    def _process_ns_specific_metering_label(self, router, ext_dev, im):
        '''Process metering label based on the associated namespaces.'''
        rm = self.routers.get(router['id'])
        with IptablesManagerTransaction(im):
            labels = router.get(constants.METERING_LABEL_KEY, [])
            for label in labels:
                ipversion = label.get('ipversion', None)
                if ipversion is None:
                    if netaddr.IPNetwork(label['FIP_ip']).version == 6:
                        ipversion = 'ipv6'
                    else:
                        ipversion = 'ipv4'
                rules = label.get('rules')
                label_chain = self._from_chain_name(label)
                label_exist = self._chain_exist(im, ipversion, label_chain)
                if not label_exist:
                    self._add_label_chain(im, label_chain, ipversion)
                if label['type'] == constants.METERING_TYPE_MULTI_EIP:
                    pf_chain, snat_chain = self._from_chain_name(
                        label, rule=True, multi=True)
                    self._check_or_add_multi_chain(im, ipversion, pf_chain)
                    self._check_or_add_multi_chain(im, ipversion, snat_chain,
                                                   True)

                    # Add rule to it's chain if label has rules.
                    separate_rules = self._separate_rules(label, rules)
                    p_rules = separate_rules.get(
                        constants.METERING_TYPE_PF_EIP, [])
                    if p_rules:
                        self._process_metering_label_rules(
                            p_rules, label_chain, pf_chain, ext_dev, im,
                            ipversion)
                    s_rules = separate_rules.get(
                        constants.METERING_TYPE_SNAT_EIP, [])
                    if s_rules:
                        self._process_metering_label_rules(
                            s_rules, label_chain, snat_chain, ext_dev, im,
                            ipversion)
                else:
                    rule_chain = self._from_chain_name(label, rule=True)
                    rule_exist = self._chain_exist(im, ipversion, rule_chain)
                    if not rule_exist:
                        is_snat = False
                        if label['type'] == constants.METERING_TYPE_SNAT_EIP:
                            is_snat = True
                        self._add_rule_chain(im, rule_chain, ipversion,
                                             is_snat=is_snat)
                    if rules:
                        # Add rule to it's chain if label has rules.
                        self._process_metering_label_rules(
                            rules, label_chain, rule_chain, ext_dev, im,
                            ipversion)
                rm.metering_labels[label['id']] = label

    def _process_associate_metering_label(self, router):
        self._update_router(router)
        rm = self.routers.get(router['id'])

        ext_dev, ext_snat_dev = self.get_external_device_names(rm)
        for (im, dev) in [(rm.iptables_manager, ext_dev),
                          (rm.snat_iptables_manager, ext_snat_dev)]:
            if im:
                self._process_ns_specific_metering_label(router, dev, im)

    def _process_ns_specific_disassociate_metering_label(self, router, im):
        """Disassociate metering label based on specific namespaces."""
        rm = self.routers.get(router['id'])
        with IptablesManagerTransaction(im):
            labels = router.get(constants.METERING_LABEL_KEY, [])
            for label in labels:
                ipversion = label['ipversion']
                label_chain = self._from_chain_name(label)
                label_exist = self._chain_exist(im, ipversion, label_chain)
                if label['id'] not in rm.metering_labels:
                    continue

                LOG.debug("Delete metering label : %s", label)
                if ipversion == 'ipv6':
                    rules_chain = self._from_chain_name(label, rule=True)
                    if label_exist:
                        im.ipv6['filter'].remove_chain(label_chain, wrap=False)
                    im.ipv6['filter'].remove_chain(rules_chain, wrap=False)
                else:
                    if label_exist:
                        im.ipv4['filter'].remove_chain(label_chain, wrap=False)
                    if label['type'] != constants.METERING_TYPE_MULTI_EIP:
                        rules_chain = self._from_chain_name(label, rule=True)
                        im.ipv4['filter'].remove_chain(rules_chain, wrap=False)
                    else:
                        pf_chain, snat_chain = self._from_chain_name(
                            label, rule=True, multi=True)
                        im.ipv4['filter'].remove_chain(pf_chain, wrap=False)
                        im.ipv4['filter'].remove_chain(snat_chain, wrap=False)

    def _process_disassociate_metering_label(self, router):
        rm = self.routers.get(router['id'])
        if not rm:
            return

        for im in [rm.iptables_manager, rm.snat_iptables_manager]:
            if im:
                self._process_ns_specific_disassociate_metering_label(
                    router, im)

        labels = router.get(constants.METERING_LABEL_KEY, [])
        for label in labels:
            label_id = label['id']
            if rm.metering_labels.get(label_id, None):
                del rm.metering_labels[label_id]

    def _process_address_group(self, router_id, ipversion="ipv4"):
        rm = self.routers.get(router_id)

        for im in [rm.ipsets_manager, rm.snat_ipsets_manager]:
            if not im:
                continue
            for ag in rm.router.get(constants.ADDRESS_GROUP_KEY, []):
                member_ips = []
                for ip in ag['addresses']:
                    member_ips.append((ip, None))
                im.set_members(ag['id'], ipversion, member_ips)

    def _add_address_group(self, router, ipversion="ipv4"):
        rm = self.routers.get(router['id'])

        for im in [rm.ipsets_manager, rm.snat_ipsets_manager]:
            if not im:
                continue
            for ag in router.get(constants.ADDRESS_GROUP_KEY, []):
                member_ips = []
                for ip in ag['addresses']:
                    member_ips.append((ip, None))
                im.set_members(ag['id'], ipversion, member_ips)

    def _remove_address_group(self, router, ipversion="ipv4"):
        new_ags = router.get(constants.ADDRESS_GROUP_KEY, [])
        new_ipset_ids = list(self.get_ipset_name(new_ag['id'])
                             for new_ag in new_ags)

        rm = self.routers.get(router['id'])
        for im in [rm.ipsets_manager, rm.snat_ipsets_manager]:
            if not im:
                continue
            need_del_ipsets = list(ipset_id for ipset_id in
                                   im.ipset_sets.keys()
                                   if ipset_id not in new_ipset_ids)
            for ipset in need_del_ipsets:
                im._destroy(ipset, ipversion)

    def _update_address_group(self, router, ipversion="ipv4"):
        rm = self.routers.get(router['id'])
        exist_ags = rm.router.get(constants.ADDRESS_GROUP_KEY, [])
        for new_ag in router.get(constants.ADDRESS_GROUP_KEY, []):
            find = False
            for old_ag in exist_ags:
                if new_ag['id'] == old_ag['id']:
                    exist_ags.remove(old_ag)
                    exist_ags.append(new_ag)
                    find = True
            if not find:
                exist_ags.append(new_ag)
        self._process_address_group(router['id'])

    def get_ipset_name(self, address_group_id, ipversion="ipv4"):
        im = ipset_manager.IpsetManager()
        return im.get_name(address_group_id, ipversion)

    @log_helpers.log_method_call
    def add_metering_label(self, context, routers):
        for router in routers:
            self._process_associate_metering_label(router)

    @log_helpers.log_method_call
    def add_metering_label_rule(self, context, routers):
        for router in routers:
            self._add_address_group(router)
            self._add_metering_label_rule(router)

    @log_helpers.log_method_call
    def remove_metering_label_rule(self, context, routers):
        for router in routers:
            self._remove_metering_label_rule(router)

    @log_helpers.log_method_call
    def update_metering_label_rules(self, context, routers):
        for router in routers:
            self._update_address_group(router)
            # self._update_metering_label_rules(router)

    def _add_metering_label_rule(self, router):
        self._process_metering_rule_action(router, 'create')

    def _remove_metering_label_rule(self, router):
        self._process_metering_rule_action(router, 'delete')

    def _process_metering_rule_action_based_on_ns(
            self, router, action, ext_dev, im):
        '''Process metering rule actions based specific namespaces.'''
        rm = self.routers.get(router['id'])
        with IptablesManagerTransaction(im):
            labels = router.get(constants.METERING_LABEL_KEY, [])
            for label in labels:
                label_id = label['id']
                ipversion = label.get('ipversion')
                label_chain = self._from_chain_name(label)
                rule_chain = ''
                label_exist = self._chain_exist(im, ipversion, label_chain)
                rule = label.get('rules', {})
                if action == 'create':
                    if not label_exist:
                        self._add_label_chain(im, label_chain, ipversion)
                        rm.metering_labels[label_id] = label
                    if label['type'] == constants.METERING_TYPE_MULTI_EIP:
                        pf_chain, snat_chain = self._from_chain_name(
                            label, rule=True, multi=True)
                        self._check_or_add_multi_chain(im, ipversion, pf_chain)
                        self._check_or_add_multi_chain(im, ipversion,
                                                       snat_chain, True)
                        if rule:
                            if rule['type'] == constants.METERING_TYPE_PF_EIP:
                                rule_chain = pf_chain
                            else:
                                rule_chain = snat_chain
                            self._add_rule_to_chain(ext_dev, rule, im,
                                                    label_chain, rule_chain,
                                                    ipversion)
                    else:
                        rule_chain = self._from_chain_name(label, rule=True)
                        self._check_or_add_multi_chain(im, ipversion,
                                                       rule_chain)
                        if rule:
                            self._add_rule_to_chain(ext_dev, rule, im,
                                                    label_chain, rule_chain,
                                                    ipversion)
                elif action == 'delete':
                    if not rule:
                        return
                    if label['type'] == constants.METERING_TYPE_MULTI_EIP:
                        if rule['type'] == constants.METERING_TYPE_PF_EIP:
                            rule_chain = WRAP_NAME + PF_RULE + label['id']
                        else:
                            rule_chain = WRAP_NAME + SNAT_RULE + label['id']
                        self._remove_rule_from_chain(ext_dev, rule, im,
                                                     label_chain, rule_chain,
                                                     ipversion)
                        if rule['type'] == constants.METERING_TYPE_SNAT_EIP:
                            im.ipv4['filter'].remove_chain(rule_chain,
                                                           wrap=False)
                    else:
                        if label['type'] == rule['type']:
                            rule_chain = self._from_chain_name(label, True)
                            self._remove_rule_from_chain(ext_dev, rule, im,
                                                         label_chain,
                                                         rule_chain, ipversion)
                        else:
                            p_chain, s_chain = self._from_chain_name(
                                label, rule=True, multi=True)
                            if rule['type'] == constants.METERING_TYPE_PF_EIP:
                                im.ipv4['filter'].remove_chain(p_chain,
                                                               wrap=False)
                            else:
                                im.ipv4['filter'].remove_chain(s_chain,
                                                               wrap=False)

    def _process_metering_rule_action(self, router, action):
        rm = self.routers.get(router['id'])
        if not rm:
            return

        ext_dev, ext_snat_dev = self.get_external_device_names(rm)
        for (im, dev) in [(rm.iptables_manager, ext_dev),
                          (rm.snat_iptables_manager, ext_snat_dev)]:
            if im and dev:
                self._process_metering_rule_action_based_on_ns(
                    router, action, dev, im)

    def _update_metering_label_rules_based_on_ns(self, router, ext_dev, im):
        '''Update metering lable rules based on namespace.'''
        with IptablesManagerTransaction(im):
            labels = router.get(constants.METERING_LABEL_KEY, [])
            for label in labels:
                ipversion = label['ipversion']
                label_chain = self._from_chain_name(label)
                rules_chain = self._from_chain_name(label, rule=True)
                if ipversion == 'ipv6':
                    im.ipv6['filter'].empty_chain(rules_chain, wrap=False)
                else:
                    im.ipv4['filter'].empty_chain(rules_chain, wrap=False)

                rules = label.get('rules')
                if rules:
                    self._process_metering_label_rules(
                        rules, label_chain, rules_chain, ext_dev, im,
                        ipversion)

    def _update_metering_label_rules(self, router):
        rm = self.routers.get(router['id'])
        if not rm:
            return

        ext_dev, ext_snat_dev = self.get_external_device_names(rm)
        for (im, dev) in [(rm.iptables_manager, ext_dev),
                          (rm.snat_iptables_manager, ext_snat_dev)]:
            if im and dev:
                self._update_metering_label_rules_based_on_ns(router, dev, im)

    @log_helpers.log_method_call
    def remove_metering_label(self, context, routers):
        for router in routers:
            self._process_disassociate_metering_label(router)

    @log_helpers.log_method_call
    def get_traffic_counters(self, context, routers):
        accs = {}
        routers_to_reconfigure = set()
        for router in routers:
            rm = self.routers.get(router['id'])
            if not rm:
                continue

            for label_id in rm.metering_labels:
                try:
                    chain = iptables_manager.get_chain_name(WRAP_NAME +
                                                            LABEL +
                                                            label_id,
                                                            wrap=False)

                    chain_acc = rm.iptables_manager.get_traffic_counters(
                        chain, wrap=False, zero=True)
                except RuntimeError:
                    LOG.exception('Failed to get traffic counters, '
                                  'router: %s', router)
                    routers_to_reconfigure.add(router['id'])
                    continue

                if not chain_acc:
                    continue

                acc = accs.get(label_id, {'pkts': 0, 'bytes': 0})

                acc['pkts'] += chain_acc['pkts']
                acc['bytes'] += chain_acc['bytes']

                accs[label_id] = acc

        for router_id in routers_to_reconfigure:
            del self.routers[router_id]

        return accs

    @log_helpers.log_method_call
    def get_rule_level_traffic_counters(self, context, routers):
        accs = {}
        routers_to_reconfigure = set()
        for router in routers:
            rm = self.routers.get(router['id'])
            if not rm:
                continue

            for label_id in rm.metering_labels:
                acc = accs.get(label_id,
                               {'ingress_pkts': 0,
                                'ingress_bytes': 0,
                                'egress_pkts': 0,
                                'egress_bytes': 0})
                try:
                    chain = iptables_manager.get_chain_name(WRAP_NAME +
                                                            RULE +
                                                            label_id,
                                                            wrap=False)

                    chain_acc = \
                        rm.iptables_manager.get_up_down_traffic_counters(
                            chain, wrap=False, zero=True)

                except RuntimeError:
                    LOG.exception('Failed to get traffic counters, '
                                  'router: %s', router)
                    routers_to_reconfigure.add(router['id'])
                    continue

                if not chain_acc:
                    continue

                acc['ingress_pkts'] += chain_acc['ingress_pkts']
                acc['ingress_bytes'] += chain_acc['ingress_bytes']
                acc['egress_pkts'] += chain_acc['egress_pkts']
                acc['egress_bytes'] += chain_acc['egress_bytes']
                accs[label_id] = acc

        for router_id in routers_to_reconfigure:
            del self.routers[router_id]

        return accs

    @log_helpers.log_method_call
    def get_rules_traffic_counters(self, context, routers):
        accs = {}
        for router in routers:
            rm = self.routers.get(router['id'])
            if not rm:
                continue
            labels = router.get(constants.METERING_LABEL_KEY, [])
            for label_id in rm.metering_labels:
                label = {}
                for label_dict in labels:
                    if label_dict['id'] == label_id:
                        label = label_dict
                if not label:
                    continue
                acc = accs.get(label_id, {})
                acc['rules_traffic'] = self._get_router_rules_traffic_counters(
                    router, label)
                accs[label_id] = acc
        return accs

    def _get_router_rules_traffic_counters(self, router, label):
        rm = self.routers.get(router['id'])
        ext_dev, ext_snat_dev = self.get_external_device_names(rm)
        rules = label.get('rules', [])
        rule = {}
        for rule in rules:
            rule['pkts'] = 0
            rule['bytes'] = 0
        label_chain = self._from_chain_name(label)
        rule_chains = []
        if label['type'] == constants.METERING_TYPE_MULTI_EIP:
            pf_chain, snat_chain = self._from_chain_name(label, rule=True,
                                                         multi=True)
            rule_chains = [pf_chain, snat_chain]
        else:
            rule_chain = self._from_chain_name(label, rule=True)
            rule_chains = [rule_chain]
        for (im, dev) in [(rm.iptables_manager, ext_dev),
                          (rm.snat_iptables_manager, ext_snat_dev)]:
            if not im:
                continue
            try:
                rules_accs = []
                for rule_chain in rule_chains:
                    acc = im.get_traffic_counters_for_all_rules(
                        rule_chain, wrap=False, zero=True)
                    if acc:
                        rules_accs.extend(acc)
                        self.save_floatingip_meter_info(acc)
            except RuntimeError:
                LOG.exception('Failed to get rules traffic counters, '
                              'router: %s', router)
                # del self.routers[router['id']
                continue
            for ruler in rules:
                for ag in router.get(constants.ADDRESS_GROUP_KEY, []):
                    if ag['id'] == ruler['address_group_id']:
                        ruler['address_group_name'] = ag['name']
                ipt_rule = self._prepare_rule(dev, ruler, label_chain)
                for rule_acc in rules_accs:
                    if self._is_this_rule(ipt_rule, rule_acc['iptables_rule']):
                        ruler['pkts'] = ruler.get('pkts', 0) + rule_acc['pkts']
                        ruler['bytes'] = \
                            ruler.get('bytes', 0) + rule_acc['bytes']
                        LOG.debug("---- match success ------")
                        break

        return rules

    def _is_this_rule(self, rule_str, rule_list):
        LOG.debug("collect original rule: %s", rule_list)

        if "--" not in rule_list:
            rule_list.insert(2, "--")

        if len(rule_list) < 7:
            return False
        # match source and destination ip
        if rule_list[5] != '0.0.0.0/0' and rule_list[5] != '::/0':
            match_str = '-s %s' % netaddr.IPNetwork(rule_list[5]).cidr
        elif rule_list[6] != '0.0.0.0/0' and rule_list[6] != '::/0':
            match_str = '-d %s' % netaddr.IPNetwork(rule_list[6]).cidr
        else:
            LOG.info("Match rule error.")
            return False
        # match in and out dev
        if rule_list[3] != '*':
            match_str = '%s -i %s' % (match_str, rule_list[3])
        elif rule_list[4] != '*':
            match_str = '%s -o %s' % (match_str, rule_list[4])
        else:
            return False

        # match protocol and port number
        if len(rule_list) > 7 and \
                (rule_list[7] == "tcp" or rule_list[7] == "udp"):
            if rule_list[8].split(':')[0] == "dpt":
                match_str = '%s -p %s --dport %s' % (
                    match_str, rule_list[7], rule_list[8].split(':')[1])
            elif rule_list[8].split(':')[0] == "spt":
                match_str = '%s -p %s --sport %s' % (
                    match_str, rule_list[7], rule_list[8].split(':')[1])
            elif rule_list[8].split(':')[0] == "dpts":
                parts = rule_list[8].split(':')
                port_range = ':'.join(parts[1:])
                match_str = '%s -p %s --dport %s' % (
                    match_str, rule_list[7], port_range)
            elif rule_list[8].split(':')[0] == "spts":
                parts = rule_list[8].split(':')
                port_range = ':'.join(parts[1:])
                match_str = '%s -p %s --sport %s' % (
                    match_str, rule_list[7], port_range)
            else:
                LOG.info("Match rule error.")
                return False

        if len(rule_list) > 7 and rule_list[7] == 'match-set':
            match_str = '%s -m set --match-set %s %s' % (match_str,
                rule_list[8], rule_list[9])
        # match chian name
        match_str = '%s -j %s' % (match_str, rule_list[0])
        LOG.debug("_prepare_rule generate rule: %s", rule_str)
        LOG.debug("collect match rule: %s", match_str)

        return match_str == rule_str

    def save_floatingip_meter_info(self, acc):
        if not cfg.CONF.enable_fip_cache:
            LOG.debug("Not support save floating ip info cache, because "
                      "enable_fip_cache = False")
            return
        chain_name = ''
        for entry in acc:
            if not entry:
                continue
            chain_name = entry['iptables_rule'][0]
        if not chain_name:
            return

        fip_cache_dir = os.path.join(self.conf.state_path, "fip_cache")
        if not os.path.exists(fip_cache_dir):
            try:
                os.makedirs(fip_cache_dir)
                LOG.info("Recreated fip cache file path: %s", fip_cache_dir)
                fileutils.ensure_tree(fip_cache_dir, mode=0o755)
            except OSError as e:
                LOG.error("Failed to create directory for fip cache %s: %s",
                          fip_cache_dir, e)
                return

        fip_meter_path = os.path.join(fip_cache_dir, chain_name)
        if not os.path.exists(fip_meter_path):
            try:
                with open(fip_meter_path, "w"):
                    LOG.info("Recreated fip meter chain file path: %s",
                             fip_meter_path)
            except OSError as e:
                LOG.error("Failed to create file path for fip meter chain "
                          "%s: %s", fip_meter_path, e)
                return
        try:
            with open(fip_meter_path, "a") as file:
                timestamp = int(time.time())
                file.write("%s: %s\n" % (timestamp, acc))
                LOG.debug("Write to fip meter chain file: %s", fip_meter_path)
        except IOError as e:
            LOG.error("Failed to write to fip meter chain file %s: %s",
                      fip_meter_path, e)

    @log_helpers.log_method_call
    def clean_metering_router_iptable(self, context, routers):
        for router in routers:
            self.clean_metering_one_router_iptable(context, router)

    def _clean_chain_and_rules(self, label, im):
        # clean all the label
        label_chain = self._from_chain_name(label)
        rules_chain = self._from_chain_name(label, rule=True)

        LOG.info("Cleaning rules_chain=%s label_chain=%s",
                 rules_chain, label_chain)
        ipversion = label['ipversion']
        if ipversion == 'ipv6':
            im.ipv6['filter'].remove_chain(
                label_chain, wrap=False)
            im.ipv6['filter'].remove_chain(
                rules_chain, wrap=False)
        else:
            im.ipv4['filter'].remove_chain(
                label_chain, wrap=False)
            im.ipv4['filter'].remove_chain(
                rules_chain, wrap=False)

    @log_helpers.log_method_call
    def clean_iptable_by_router_id(self, rm, floatingip_id):
        for im in [rm.iptables_manager, rm.snat_iptables_manager]:
            if im:
                with IptablesManagerTransaction(im):
                    labels = rm.router.get(
                        constants.METERING_LABEL_KEY, [])
                    LOG.info("Cleaning labels=%s floatingip_id=%s",
                             labels, floatingip_id)
                    for label in labels:
                        LOG.info("Cleaning aa label=%s", label)
                        if floatingip_id:
                            # only clean the specify lable equal the floating
                            if floatingip_id == label['FIP_uuid']:
                                LOG.info("Cleaning specify label=%s", label)
                                self._clean_chain_and_rules(label, im)
                                break
                        else:
                            LOG.info("Cleaning all label in router ns=%s",
                                     label)
                            self._clean_chain_and_rules(label, im)

    @log_helpers.log_method_call
    def _clean_metering_label(self, rm, label_id, label_type):
        for im in [rm.iptables_manager, rm.snat_iptables_manager]:
            if not im:
                continue
            with IptablesManagerTransaction(im):
                labels = rm.router.get(constants.METERING_LABEL_KEY, [])
                for label in labels:
                    if label['id'] == label_id:
                        self.clean_label_chain(im, label,
                                               label_type=label_type)

    def clean_label_chain(self, im, label, label_type='snat'):
        rule_chain = ''
        rules_chain = self._from_chain_name(label, rule=True, multi=True)
        for chain in rules_chain:
            if label_type.startswith('snat') and \
                    chain.startswith(WRAP_NAME + SNAT_RULE):
                rule_chain = chain
            elif label_type.startswith('pf') and \
                chain.startswith(WRAP_NAME + SNAT_RULE):
                rule_chain = chain
        LOG.info("clean label rule chain:%s.", rule_chain)
        if not rule_chain:
            return
        version = label['ipversion']
        if version == 'ipv6':
            im.ipv6['filter'].remove_chain(rule_chain, wrap=False)
        else:
            im.ipv4['filter'].remove_chain(rule_chain, wrap=False)

    @log_helpers.log_method_call
    def clean_metering_label_rules(self, context, router):
        router_id = router.get('router_id')
        label_id = router.get('label_id')
        label_type = router.get('label_type', 'snat_eip')
        if not label_id or not router_id:
            return
        namespace = "qrouter-" + router_id
        if not ip_lib.network_namespace_exists(namespace):
            LOG.info("Not found namespace=%s to clean metering rules.")
            return
        rm = self.routers.get(router_id, None)
        if rm:
            self._clean_metering_label(rm, label_id, label_type)

    @log_helpers.log_method_call
    def clean_metering_one_router_iptable(self, context, router):
        LOG.info("Clean metering iptable rule in router = %s", router)
        router_id = router['id']
        ver = router['ver']
        floatingip_id = router['floatingip_id']

        cmd_table_save = []
        cmd_table = []

        if ver == 'ipv4':
            cmd_table_fmt = 'iptables'
            cmd_table.append(cmd_table_fmt)
        elif ver == 'ipv6':
            cmd_table_fmt = 'ip6tables'
            cmd_table.append(cmd_table_fmt)

        else:
            return

        cmd_table_save_fmt = '%s-save' % cmd_table_fmt
        cmd_table_save.append(cmd_table_save_fmt)

        namespace = "qrouter-" + router_id
        LOG.info("namespace= %s", namespace)
        if (not ip_lib.network_namespace_exists(namespace)):
            LOG.warning("Not found router namespace=%s for cleanning iptable")
            return

        rm = self.routers.get(router_id, None)

        if rm:
            self.clean_iptable_by_router_id(rm, floatingip_id)
            # if iptable manager exist, cleaned finish then return
            LOG.info("Clean iptable metering rule by iptable mgr success !")
            return

        # if iptable manager has been deleted
        LOG.info("=namespace=%s cleanning=", namespace)
        outcmd = ['ip', 'netns', 'exec', namespace] + cmd_table_save

        try:
            output = linux_utils.execute(outcmd, run_as_root=True)
        except RuntimeError:
            # We could be racing with a cron job deleting namespaces.
            # It is useless to try to apply iptables rules over and
            # over again in a endless loop if the namespace does not
            # exist.
            with excutils.save_and_reraise_exception() as ctx:
                if (namespace and not
                        ip_lib.network_namespace_exists(namespace)):
                    ctx.reraise = False
                    LOG.error("Found namespace %s was deleted during IPTables "
                              "operations.", namespace)
                    return

        t1 = time.time()
        LOG.debug("\n  start time=%s", t1)
        all_lines = output.split('\n')
        start, end = self._find_table_filter(all_lines, 'filter')
        old_filter_table_rules = all_lines[start:end]

        action_j_chain, action_rule = self._find_label_rule(
            old_filter_table_rules)
        LOG.info("\n action_j_chain=%s", action_j_chain)
        self._clean_forward_j_chain(cmd_table, action_j_chain, router_id)
        LOG.info("\n clean_action=%s", action_rule)
        self._clean_iptable_rules(cmd_table, action_rule, router_id)
        LOG.debug("\n  escap time=%s", time.time() - t1)

        return

    def _find_table_filter(self, lines, table_name):
        if len(lines) < 3:
            # length only <2 when fake iptables
            return (0, 0)

        table_name = "filter"
        try:
            start = lines.index('*%s' % table_name)
        except ValueError:
            # Couldn't find table_name
            LOG.debug('Unable to find table %s', table_name)
            return (0, 0)
        end = lines[start:].index('COMMIT') + start + 1
        return (start, end)

    def _clean_chain(self, meter_rule, prefix=None):
        filter_table = []

        LABEL_CHAIN = ':neutron-meter-l-'
        RULE_CHAIN = ':neutron-meter-r-'
        LABEL_A = '-A neutron-meter-l-'
        RULE_A = '-A neutron-meter-r-'
        RULE_FLAG = '-A neutron-meter'

        if prefix:
            # delete specific label
            LABEL_CHAIN = LABEL_CHAIN + prefix
            RULE_CHAIN = RULE_CHAIN + prefix
            LABEL_A = LABEL_A + prefix
            RULE_A = RULE_A + prefix

        else:
            # delete all label
            for line in meter_rule:
                if line.startswith(LABEL_CHAIN) or line.startswith(
                    RULE_CHAIN) or line.startswith(RULE_FLAG):
                    continue
                else:
                    filter_table.append(line)

        return filter_table

    def _find_label_rule(self, filter_rules, prefix=None):
        LABEL_PREFIX = '-A neutron-meter-'
        LABEL_PREFIX_LEN = 17
        LABEL_A = 'l'
        LABEL_F = 'F'
        label_to_remove = []
        label_j_chain_to_remove = []

        if prefix:
            # delete specific label
            LABEL_A = LABEL_PREFIX + LABEL_A + prefix
            # to do

        else:
            # delete all label
            for line in filter_rules:
                if line.startswith(LABEL_PREFIX):
                    if line[LABEL_PREFIX_LEN] == LABEL_A:
                        action_dict = {
                            'a1': None,
                            'a2': None,
                            'a3': None,
                            'a4': None,
                            'a5': None,
                            'a6': None}
                        label_uuid = line[19::]
                        action_dict['a1'] = '-D neutron-meter-l-' + \
                            label_uuid + ' 1'
                        action_dict['a2'] = '-D neutron-meter-r-' + \
                            label_uuid + ' 1'
                        action_dict['a3'] = '-D neutron-meter-r-' + \
                            label_uuid + ' 1'
                        action_dict['a4'] = '-X neutron-meter-l-' + label_uuid
                        action_dict['a5'] = '-X neutron-meter-r-' + label_uuid
                        label_to_remove.append(action_dict)
                    elif line[LABEL_PREFIX_LEN] == LABEL_F:
                        j_action = '-D' + line[2::]
                        label_j_chain_to_remove.append(j_action)

        return (label_j_chain_to_remove, label_to_remove)

    def _execut_iptable_cmd(self, router_id, cmd, action_dict):
        namespace = "qrouter-" + router_id

        for i in range(1, 6):
            key = 'a%s' % i
            try:
                kwargs = {'log_fail_as_error': False}
                arg = action_dict[key].split(' ')
                cmd_ns = ['ip', 'netns', 'exec', namespace] + cmd + arg
                LOG.debug("\n %s cmd:", key)

                commands = [action_dict[key]]
                commands.append('')
                linux_utils.execute(cmd_ns, run_as_root=True, **kwargs)
            except RuntimeError:
                # We could be racing with a cron job deleting namespaces.
                # It is useless to try to apply iptables rules over and
                # over again in a endless loop if the namespace does not
                # exist.
                LOG.warning("RuntimeError %s", cmd_ns)

    def _clean_iptable_rules(self, cmd, clean_action, router_id):
        for ac in clean_action:
            try:
                self._execut_iptable_cmd(router_id, cmd, ac)
            except RuntimeError:
                # We could be racing with a cron job deleting namespaces.
                # It is useless to try to apply iptables rules over and
                # over again in a endless loop if the namespace does not
                # exist.
                LOG.warning("RuntimeError %s %s", router_id, ac)

    def _clean_forward_j_chain(self, cmd, action_j_chain, router_id):
        namespace = "qrouter-" + router_id
        for ac in action_j_chain:
            try:
                cmd_ns = ['ip', 'netns', 'exec',
                    namespace] + cmd + ac.split(' ')
                linux_utils.execute(cmd_ns, run_as_root=True)
            except RuntimeError:
                # We could be racing with a cron job deleting namespaces.
                # It is useless to try to apply iptables rules over and
                # over again in a endless loop if the namespace does not
                # exist.
                LOG.warning("RuntimeError %s ", cmd_ns)

    @log_helpers.log_method_call
    def sync_router_namespaces(self, context, routers):
        for router in routers:
            rm = self.routers.get(router['id'])
            if not rm:
                continue

            # NOTE(bno1): Sometimes a router is added before its namespaces are
            # created. The metering agent has to periodically check if the
            # namespaces for the missing iptables managers have appearead and
            # create the managers for them. When a new manager is created, the
            # metering rules have to be added to it.
            if rm.create_iptables_managers():
                self._process_associate_metering_label(router)
