#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import mock
from neutron_lib import constants

from oslo_utils import uuidutils

from neutron.agent.l3.extensions.qos import base as qos_base
from neutron.agent.l3.extensions.qos import fip_shared as fip_shared_qos
from neutron.api.rpc.handlers import resources_rpc
from neutron.objects.qos import policy
from neutron.objects.qos import rule
from neutron.tests.unit.agent.l3.extensions.qos import test_fip


_uuid = uuidutils.generate_uuid


class SharedFipQosAgentExtTestCase(test_fip.QosExtensionBaseTestCase):

    def setUp(self):
        super(SharedFipQosAgentExtTestCase, self).setUp()
        self.fip_shared_qos_ext = fip_shared_qos.SharedFipQosAgentExtension()
        self.context = mock.Mock()
        self.fip_shared_qos_ext.initialize(self.connection,
                                           constants.L3_AGENT_MODE)
        self.policy_id = _uuid()
        self.qos_policies = {}

    def _set_pull_mock(self, raise_exception=False):

        def _pull_mock(context, resource_type, resource_id):
            if raise_exception:
                raise resources_rpc.ResourceNotFound(
                    resource_type=resource_type, resource_id=resource_id)
            else:
                self.set_test_qos_rules(context, resource_id)
                return self.qos_policies[resource_id]

        self.pull = mock.patch('neutron.api.rpc.handlers.resources_rpc.'
                               'ResourcesPullRpcApi.pull').start()
        self.pull.side_effect = _pull_mock

    def set_test_qos_rules(self, context, policy_id):
        qos_policy_rule_1 = rule.QosBandwidthLimitRule(
            context=context,
            qos_policy_id=policy_id,
            id=_uuid(),
            max_kbps=1000,
            max_burst_kbps=100,
            direction=constants.INGRESS_DIRECTION)
        qos_policy_rule_2 = rule.QosBandwidthLimitRule(
            context=context,
            qos_policy_id=policy_id,
            id=_uuid(),
            max_kbps=1000,
            max_burst_kbps=100,
            direction=constants.EGRESS_DIRECTION)
        qos_policy = policy.QosPolicy(
            context=context,
            project_id=_uuid(),
            id=policy_id,
            name="Test Policy Name",
            description="This is a policy for testing purposes",
            shared=False,
            rules=[qos_policy_rule_1, qos_policy_rule_2])
        qos_policy.obj_reset_changes()
        self.qos_policies[policy_id] = qos_policy

    def test_get_fip_qos_rates(self):
        # Case 1: Test to get qos policy by None
        policy_id = None
        actual = self.fip_shared_qos_ext.get_fip_qos_rates(
            self.context, policy_id)
        expected = {
            constants.INGRESS_DIRECTION: {
                "rate": qos_base.IP_DEFAULT_RATE,
                "burst": qos_base.IP_DEFAULT_BURST
            },
            constants.EGRESS_DIRECTION: {
                "rate": qos_base.IP_DEFAULT_RATE,
                "burst": qos_base.IP_DEFAULT_BURST
            }
        }
        self.assertEqual(expected, actual)
        # 1.1 Test whether should add fip filter
        actual = self.fip_shared_qos_ext._should_add_fip_filter(
            constants.INGRESS_DIRECTION, self.policy_id)
        self.assertFalse(actual)
        actual = self.fip_shared_qos_ext._should_add_fip_filter(
            constants.EGRESS_DIRECTION, self.policy_id)
        self.assertFalse(actual)

        # Case 2: Test to get qos policy raising ResourceNotFound
        policy_id = self.policy_id
        self._set_pull_mock(True)
        with mock.patch.object(self.fip_shared_qos_ext,
                               'qos_map') as mock_qos_map:
            actual = self.fip_shared_qos_ext.get_fip_qos_rates(
                self.context, policy_id)
            self.assertEqual(expected, actual)
            mock_qos_map._clean_policy_info.assert_called_once_with(policy_id)

            # 2.1 Test whether should add fip filter
            actual = self.fip_shared_qos_ext._should_add_fip_filter(
                constants.INGRESS_DIRECTION, self.policy_id)
            self.assertFalse(actual)
            actual = self.fip_shared_qos_ext._should_add_fip_filter(
                constants.EGRESS_DIRECTION, self.policy_id)
            self.assertFalse(actual)

        # Case 3: Test to get a qos policy
        self._set_pull_mock()
        actual = self.fip_shared_qos_ext.get_fip_qos_rates(
            self.context, policy_id)
        expected = {
            'ingress': {
                'rate': 1000,
                'burst': 100
            },
            'egress': {
                'rate': 1000,
                'burst': 100
            }
        }
        self.assertOrderedEqual(expected, actual)
        self.assertIn(self.policy_id,
                      self.fip_shared_qos_ext.qos_map.known_policies)
        expected_qos_policy = self.fip_shared_qos_ext.qos_map.known_policies[
            self.policy_id]
        self.assertEqual(2, len(expected_qos_policy.rules))

        # 3.1 Test whether should add fip filter
        actual = self.fip_shared_qos_ext._should_add_fip_filter(
            constants.INGRESS_DIRECTION, self.policy_id)
        self.assertTrue(actual)
        actual = self.fip_shared_qos_ext._should_add_fip_filter(
            constants.EGRESS_DIRECTION, self.policy_id)
        self.assertTrue(actual)

    def test_should_add_fip_filter(self):
        # Case 1: Test to get qos policy raising ResourceNotFound
        self._set_pull_mock(True)
        self.fip_shared_qos_ext.get_fip_qos_rates(self.context, self.policy_id)
        actual = self.fip_shared_qos_ext._should_add_fip_filter(
            constants.INGRESS_DIRECTION, self.policy_id)
        self.assertFalse(actual)
        actual = self.fip_shared_qos_ext._should_add_fip_filter(
            constants.EGRESS_DIRECTION, self.policy_id)
        self.assertFalse(actual)

        # Case 2: Test to get a qos policy
        self._set_pull_mock()
        self.fip_shared_qos_ext.get_fip_qos_rates(self.context, self.policy_id)
        actual = self.fip_shared_qos_ext._should_add_fip_filter(
            constants.INGRESS_DIRECTION, self.policy_id)
        self.assertTrue(actual)
        actual = self.fip_shared_qos_ext._should_add_fip_filter(
            constants.EGRESS_DIRECTION, self.policy_id)
        self.assertTrue(actual)
