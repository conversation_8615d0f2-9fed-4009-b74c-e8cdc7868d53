#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import functools

import netaddr
from neutron_lib.callbacks import events
from neutron_lib.callbacks import registry
from neutron_lib.callbacks import resources
from neutron_lib import constants as lib_consts
from neutron_lib.db import utils as db_utils
from neutron_lib import exceptions as n_exc
from neutron_lib.objects import exceptions as obj_exc
from neutron_lib.plugins import constants
from neutron_lib.plugins import directory
from oslo_config import cfg
from oslo_log import log as logging

from neutron._i18n import _
from neutron.common import utils
from neutron.conf.services import route_table as route_table_conf
from neutron.db import _resource_extend as resource_extend
from neutron.db import api as db_api
from neutron.db import db_base_plugin_common
from neutron.db.models import route_table as models
from neutron.extensions import _route_table as apidef
from neutron.extensions import route_table as ext_rt
from neutron.objects import base as base_obj
from neutron.objects import route_table as route_table_obj
from neutron.services.route_table.common import exceptions as rt_exc
from neutron.services.route_table.drivers import l3 as l3rpc

LOG = logging.getLogger(__name__)
route_table_conf.register_route_table_opts()

# IP rule table id, start from 200
ROUTE_TABLE_ID = 200


def make_result_with_fields(f):
    @functools.wraps(f)
    def inner(*args, **kwargs):
        fields = kwargs.get('fields')
        result = f(*args, **kwargs)
        if fields is None:
            return result
        elif isinstance(result, list):
            return [db_utils.resource_fields(r, fields) for r in result]
        else:
            return db_utils.resource_fields(result, fields)

    return inner


@resource_extend.has_resource_extenders
@registry.has_registry_receivers
class RouteTablePlugin(ext_rt.RouteTablePluginBase):
    """Implementation of the Neutron Route Table Service Plugin.

    This class implements a Route Table plugin.
    """

    required_service_plugins = ['router']
    supported_extension_aliases = [apidef.ALIAS]

    __native_pagination_support = True
    __native_sorting_support = True
    __filter_validation_support = True

    def __init__(self):
        super(RouteTablePlugin, self).__init__()
        self.l3_plugin = directory.get_plugin(constants.L3)
        self._core_plugin = directory.get_plugin()
        self.driver = l3rpc.L3RpcDriver()

    def _get_route_table(self, context, id):
        obj = route_table_obj.RouteTable.get_object(context, id=id)
        if obj is None:
            raise rt_exc.RouteTableNotFound(id=id)
        return obj

    def _get_default_rt_id(self, context, router_id):
        """Fetch the default route table ID for the given router."""
        default_rt = route_table_obj.DefaultRouteTable.get_object(
            context, router_id=router_id)
        if default_rt:
            return default_rt.routetable_id

    def _ensure_default_route_table(self, context, router_id):
        """Ensure the default route table exists, if not create one."""
        default_rt_id = self._get_default_rt_id(context, router_id)
        if default_rt_id:
            return default_rt_id

        route_table = {
            'route_table':
                {'name': 'default',
                 'description': 'Default Route Table',
                 'router_id': router_id,
                 'project_id': context.project_id}
        }
        return self.create_route_table(context, route_table,
                                       default_rt=True)['id']

    @staticmethod
    def _make_route_table_routes_list(routes):
        return [{'destination': str(route['destination']),
                 'nexthop': str(route['nexthop']),
                 'type': route['type']}
                for route in routes]

    @staticmethod
    def _make_route_table_routes_info(route_table_id, tenant_id, routes):
        return {
            'route_table_id': route_table_id,
            'tenant_id': tenant_id,
            'routes': routes
        }

    @staticmethod
    def _make_route_table_bindings_list(bindings):
        return [bind['subnet_id'] for bind in bindings]

    @staticmethod
    def _make_route_table_bindings_info(route_table_id, tenant_id, bindings):
        return {
            'route_table_id': route_table_id,
            'tenant_id': tenant_id,
            'subnets': bindings
        }

    def _make_route_table_dict(self, route_table, fields=None):
        res = {'id': route_table['id'],
               'name': route_table['name'],
               'router_id': route_table['router_id'],
               'tenant_id': route_table['tenant_id'],
               'description': route_table['description'],
               'table_id': route_table['table_id']}
        if route_table.routes:
            res['routes'] = self._make_route_table_routes_list(
                route_table.routes)
        else:
            res['routes'] = []
        if route_table.subnets:
            res['subnets'] = self._make_route_table_bindings_list(
                route_table.subnets)
        else:
            res['subnets'] = []
        res['default'] = route_table.is_default
        resource_extend.apply_funcs(apidef.COLLECTION_NAME, res,
                                    route_table.db_obj)
        return db_utils.resource_fields(res, fields)

    @make_result_with_fields
    @db_base_plugin_common.convert_result_to_dict
    def get_route_table_routes(self, context, filters=None, fields=None,
                               sorts=None, limit=None, marker=None,
                               page_reverse=False):
        filters = filters or {}
        pager = base_obj.Pager(sorts, limit, page_reverse, marker)
        routes_obj = route_table_obj.RouteTableRoutes.get_objects(
            context, _pager=pager, **filters)
        return routes_obj

    def _get_routes_by_route_table_id(self, context, id):
        routes_obj = route_table_obj.RouteTableRoutes.get_objects(
            context, routetable_id=id)
        return self._make_route_table_routes_list(routes_obj)

    def _get_routes_by_router_id(self, context, router_id):
        qry_filter = context.session.query(models.RouteTable.id).filter(
            models.RouteTable.router_id == router_id)
        query = context.session.query(models.RouteTableRoutes)
        query = query.filter(
            models.RouteTableRoutes.routetable_id.in_(qry_filter))
        return self._make_route_table_routes_list(query)

    @make_result_with_fields
    @db_base_plugin_common.convert_result_to_dict
    def get_associated_subnets(self, context, filters=None, fields=None,
                               sorts=None, limit=None, marker=None,
                               page_reverse=False):
        filters = filters or {}
        bindings = route_table_obj.RouteTableSubnetBindings.get_objects(
            context, **filters)
        return bindings

    def _get_subnet_on_router(self, context, router_id):
        context = context.elevated()
        filters = {'device_id': [router_id]}
        ports = self._core_plugin.get_ports(context, filters)
        cidrs = []
        ips = []
        for port in ports:
            for ip in port['fixed_ips']:
                cidrs.append(self._core_plugin.get_subnet(
                    context, ip['subnet_id'])['cidr'])
                ips.append(ip['ip_address'])
        return cidrs, ips

    def _validate_routes_nexthop(self, context, router_id, routes):
        cidrs, ips = self._get_subnet_on_router(context, router_id)
        for route in routes:
            nexthop = route['nexthop']
            nexthop_type = route['type']
            if nexthop_type not in cfg.CONF.supported_routes_nexthop_type:
                raise rt_exc.RoutesNexthopTypeNotSupport(nexthop=nexthop_type)

            if not netaddr.all_matching_cidrs(nexthop, cidrs):
                raise rt_exc.InvalidRoutes(
                    routes=routes,
                    reason=_('the nexthop is not connected with router'))
            # Note(nati) nexthop should not be same as fixed_ips
            if nexthop in ips:
                raise rt_exc.InvalidRoutes(
                    routes=routes,
                    reason=_('the nexthop is used by router'))

    def _validate_routes_destination(self, context, router_id, routes):
        routes_dst, ips = self._get_subnet_on_router(context, router_id)
        for route in routes:
            if route['destination'] in routes_dst:
                raise rt_exc.InvalidRoutes(
                    routes=routes,
                    reason=_('duplicate destination'))
            routes_dst.append(route['destination'])

    def _validate_route_table_routes(self, context, id, router_id, routes):
        if len(routes) > cfg.CONF.max_route_table_routes:
            raise rt_exc.RouteTableRoutesExhausted(
                routetable_id=id,
                quota=cfg.CONF.max_route_table_routes)

        msg = ext_rt.validate_routetableroutes(routes)
        if msg:
            raise n_exc.BadRequest(resource=apidef.RESOURCE_NAME, msg=msg)
        self._validate_routes_destination(context, router_id, routes)
        self._validate_routes_nexthop(context, router_id, routes)

    def _validate_associate_subnets(self, context, id, router_id, subnets):
        filters = {'device_id': [router_id],
                   'device_owner': lib_consts.ROUTER_INTERFACE_OWNERS}
        interfaces = self._core_plugin.get_ports(
            context.elevated(), filters)
        router_subnets = []
        for inf in interfaces:
            for ip in inf['fixed_ips']:
                subnet = self._core_plugin.get_subnet(
                    context.elevated(),
                    id=ip['subnet_id'])
                router_subnets.append(subnet['id'])
        for subnet in subnets:
            if subnet not in router_subnets:
                raise rt_exc.FailedToAssociateSubnets(
                    error=(_("subnet %(subnet)s is not connect to router "
                             "%(router)s") % {'subnet': subnet,
                                              'router': router_id}))

    def _assign_table_id(self, context, router_id):
        # assign table id according exist route tables id,
        # prevent id duplicate
        exist_route_tables = route_table_obj.RouteTable.get_objects(
            context, router_id=router_id)
        ids = [rt['table_id'] for rt in exist_route_tables]
        table_id = ROUTE_TABLE_ID
        for i in range(cfg.CONF.max_route_tables):
            table_id = ROUTE_TABLE_ID + i
            if table_id not in ids:
                return table_id
        return table_id

    @registry.receives(apidef.ROUTE_TABLE, [events.BEFORE_UPDATE])
    def _prevent_update_default_route_table(self, resource, event,
                                            trigger, payload=None):
        context = payload.context
        route_table_id = payload.resource_id
        rt = self._get_route_table(context, route_table_id)
        if rt.is_default:
            raise rt_exc.InvalidOperationForDefaultRouteTable(
                id=route_table_id,
                reason=_("default route table can't be updated"))

    @registry.receives(apidef.ROUTE_TABLE, [events.BEFORE_DELETE])
    def _prevent_delete_route_table(self, resource, event,
                                    trigger, payload=None):
        context = payload.context
        route_table_id = payload.resource_id
        delete_check = payload.request_body['delete_check']
        if not delete_check:
            return
        if route_table_obj.RouteTableSubnetBindings.get_objects(
                context, routetable_id=route_table_id):
            raise rt_exc.RouteTableInUse(id=route_table_id)

        rt = self._get_route_table(context, route_table_id)
        if rt.is_default:
            raise rt_exc.InvalidOperationForDefaultRouteTable(
                id=route_table_id,
                reason=_("default route table can't be deleted by api"))

    @registry.receives(resources.ROUTER, [events.AFTER_CREATE])
    def _create_default_route_table(self, resource, event, trigger, **kwargs):
        context = kwargs['context']
        router_id = kwargs['router_id']

        # Reentry prevention: Check if default route table already exists
        existing_default_rt = route_table_obj.DefaultRouteTable.get_object(
            context, router_id=router_id)
        if existing_default_rt:
            LOG.debug("Default route table already exists for router %s, skipping creation",
                     router_id)
            return

        # Default route table project_id equals with router project_id
        project_id = kwargs['router'].get('project_id')
        route_table = {
            'route_table':
                {'name': 'default',
                 'description': 'Default Route Table',
                 'router_id': router_id,
                 'project_id': project_id}
        }
        self.create_route_table(context, route_table, default_rt=True)

    @registry.receives(resources.ROUTER, [events.PRECOMMIT_DELETE])
    def _delete_default_route_table(self, resource, event, trigger, **kwargs):
        context = kwargs['context']
        router_id = kwargs['router_id']

        # Reentry prevention: Check if default route table already deleted
        rt = route_table_obj.DefaultRouteTable.get_object(
            context, router_id=router_id)
        if not rt:
            LOG.debug("Default route table already deleted for router %s, skipping",
                     router_id)
            return

        self.delete_route_table(
            context, rt.routetable_id, delete_check=False)

    @registry.receives(resources.ROUTER_INTERFACE, [events.BEFORE_DELETE])
    def _prevent_delete_router_interface_use_by_routes(
            self, resource, event, trigger, **kwargs):
        context = kwargs['context']
        router_id = kwargs['router_id']
        subnet_id = kwargs['subnet_id']

        # Reentry prevention: Check if validation already performed
        # This is a validation function, so we check if the conditions still exist
        try:
            subnet = self._core_plugin.get_subnet(context, subnet_id)
        except Exception:
            # Subnet might already be deleted, validation no longer needed
            LOG.debug("Subnet %s not found, skipping route validation", subnet_id)
            return

        subnet_cidr = netaddr.IPNetwork(subnet['cidr'])
        rt_routes = self._get_routes_by_router_id(context, router_id)

        # If no routes exist, validation is not needed
        if not rt_routes:
            LOG.debug("No routes found for router %s, skipping validation", router_id)
            return

        for route in rt_routes:
            if netaddr.all_matching_cidrs(route['nexthop'], [subnet_cidr]):
                raise rt_exc.RouterInterfaceInUseByRouteTableRoutes(
                    router_id=router_id, subnet_id=subnet_id)

    def create_route_table(self, context, route_table, default_rt=False):
        rt = route_table[apidef.RESOURCE_NAME]
        router_id = rt.get('router_id')
        rt_routes = rt.pop('routes', [])

        exist_rts = self.get_route_tables(context,
                                          {'router_id': [router_id]})
        if len(exist_rts) >= cfg.CONF.max_route_tables:
            raise rt_exc.RouteTablesExhausted(quota=cfg.CONF.max_route_tables)

        if rt_routes:
            self._validate_route_table_routes(context, '',
                                              rt['router_id'], rt_routes)

        if not default_rt:
            self._ensure_default_route_table(context, router_id)
        else:
            exist_def_rt_id = self._get_default_rt_id(context, router_id)
            if exist_def_rt_id is not None:
                return self.get_route_table(context, exist_def_rt_id)

        try:
            rt.pop('tenant_id', None)
            table_id = self._assign_table_id(context, router_id)
            rt.update({'table_id': table_id,
                       'is_default': default_rt})

            with db_api.context_manager.writer.using(context):
                rt_obj = route_table_obj.RouteTable(context, **rt)
                rt_obj.create()

                for route in rt_routes:
                    route_table_obj.RouteTableRoutes(
                        context,
                        type=route['type'],
                        routetable_id=rt_obj['id'],
                        destination=utils.AuthenticIPNetwork(
                            route['destination']),
                        nexthop=netaddr.IPAddress(route['nexthop'])).create()

        except obj_exc.NeutronDbObjectDuplicateEntry as e:
            raise rt_exc.FailedToCreateOrUpdateRouteTable(
                error=e)
        self.driver.create_route_table(context, rt_obj)
        rt_obj = route_table_obj.RouteTable.get_object(context, id=rt_obj.id)
        return self._make_route_table_dict(rt_obj)

    def get_route_table(self, context, id, fields=None):
        rt_obj = self._get_route_table(context, id)
        return self._make_route_table_dict(rt_obj)

    def get_route_tables(self, context, filters=None, fields=None, sorts=None,
                         limit=None, marker=None, page_reverse=False):
        filters = filters or {}
        pager = base_obj.Pager(sorts, limit, page_reverse, marker)
        rt_objs = route_table_obj.RouteTable.get_objects(
            context, _pager=pager, **filters)
        return [self._make_route_table_dict(rt_obj) for rt_obj in rt_objs]

    def delete_route_table(self, context, id, delete_check=True):
        rt_obj = self._get_route_table(context, id)
        data = {'delete_check': delete_check}
        registry.publish(apidef.ROUTE_TABLE, events.BEFORE_DELETE, self,
                         payload=events.DBEventPayload(
                             context, states=(rt_obj,),
                             request_body=data, resource_id=rt_obj.id))
        with db_api.context_manager.writer.using(context):
            rt_obj.delete()
        self.driver.delete_route_table(context, rt_obj)

    def update_route_table(self, context, id, route_table):
        rt = route_table[apidef.RESOURCE_NAME]
        rt_obj = self._get_route_table(context, id)
        rt.pop('routes', [])
        registry.publish(apidef.ROUTE_TABLE, events.BEFORE_UPDATE, self,
                         payload=events.DBEventPayload(
                             context, states=(rt_obj,),
                             request_body=rt, resource_id=rt_obj.id))

        try:
            with db_api.context_manager.writer.using(context):
                rt_obj.update_fields(rt)
                rt_obj.update()
        except obj_exc.NeutronDbObjectDuplicateEntry as e:
            raise rt_exc.FailedToCreateOrUpdateRouteTable(error=e)

        self.driver.update_route_table(context, rt_obj)
        return self._make_route_table_dict(rt_obj)

    def associate_subnets(self, context, id, route_table):
        rt = route_table[apidef.RESOURCE_NAME]
        rt_obj = self._get_route_table(context, id)
        if rt_obj.is_default:
            raise rt_exc.InvalidOperationForDefaultRouteTable(
                id=id,
                reason=_("default route table doesn't "
                         "support associate subnets"))
        subnets = rt['subnets']
        self._validate_associate_subnets(context, id, rt_obj['router_id'],
                                         subnets)

        try:
            route_table_obj.RouteTableSubnetBindings.delete_objects(
                context, subnet_id=subnets)
            with db_api.context_manager.writer.using(context):
                for subnet in subnets:
                    route_table_obj.RouteTableSubnetBindings(
                        context, subnet_id=subnet,
                        routetable_id=id).create()
                rt_obj.update()
        except obj_exc.NeutronDbObjectDuplicateEntry:
            raise rt_exc.FailedToAssociateSubnets(
                error=_("subnets %s have already been associated") % subnets)
        except Exception as e:
            raise rt_exc.FailedToCreateOrUpdateRouteTable(error=e)
        self.driver.update_route_table(context, rt_obj)
        bindings_list = self._make_route_table_bindings_list(rt_obj.subnets)
        return self._make_route_table_bindings_info(
            id, rt_obj.project_id, bindings_list)

    def disassociate_subnets(self, context, id, route_table):
        rt = route_table[apidef.RESOURCE_NAME]
        rt_obj = self._get_route_table(context, id)
        if rt_obj.is_default:
            raise rt_exc.InvalidOperationForDefaultRouteTable(
                id=id,
                reason=_("default route table doesn't "
                         "support disassociate subnets"))
        subnets = rt['subnets']

        try:
            with db_api.context_manager.writer.using(context):
                for subnet in subnets:
                    bindings = \
                        route_table_obj.RouteTableSubnetBindings.get_object(
                            context, subnet_id=subnet,
                            routetable_id=id)
                    if bindings:
                        bindings.delete()
                rt_obj.update()
        except Exception as e:
            raise rt_exc.FailedToCreateOrUpdateRouteTable(error=e)
        self.driver.update_route_table(context, rt_obj)
        bindings_list = self._make_route_table_bindings_list(rt_obj.subnets)
        return self._make_route_table_bindings_info(
            id, rt_obj.project_id, bindings_list)

    def add_route_table_routes(self, context, id, route_table):
        rt = route_table[apidef.RESOURCE_NAME]
        rt_obj = self._get_route_table(context, id)
        routes = rt['routes']
        old_routes = self._get_routes_by_route_table_id(context, id)
        self._validate_route_table_routes(context, id, rt_obj['router_id'],
                                          old_routes + routes)

        try:
            with db_api.context_manager.writer.using(context):
                for route in routes:
                    route_table_obj.RouteTableRoutes(
                        context,
                        type=route['type'],
                        routetable_id=id,
                        destination=utils.AuthenticIPNetwork(
                            route['destination']),
                        nexthop=netaddr.IPAddress(
                            route['nexthop'])).create()
                rt_obj.update()
        except Exception as e:
            rt_exc.FailedToCreateOrUpdateRouteTable(error=e)

        self.driver.update_route_table(context, rt_obj)
        routes_list = self._make_route_table_routes_list(rt_obj.routes)
        return self._make_route_table_routes_info(
            id, rt_obj.project_id, routes_list)

    def update_route_table_routes(self, context, id, route_table):
        rt = route_table[apidef.RESOURCE_NAME]
        routes = rt['routes']
        rt_obj = self._get_route_table(context, id)
        self._validate_route_table_routes(context, id, rt_obj['router_id'],
                                          routes)

        try:
            with db_api.context_manager.writer.using(context):
                for route in routes:
                    routes_obj = route_table_obj.RouteTableRoutes.get_object(
                        context,
                        routetable_id=id,
                        destination=route['destination'])
                    if routes_obj is None:
                        raise rt_exc.RouteTableRoutesNotFound(route=route)
                    routes_obj.update_fields(route)
                    routes_obj.update()
                rt_obj.update()
        except rt_exc.RouteTableRoutesNotFound:
            raise rt_exc.FailedToCreateOrUpdateRouteTable(
                error='route table routes not found')
        except Exception as e:
            raise rt_exc.FailedToCreateOrUpdateRouteTable(error=e)
        self.driver.update_route_table(context, rt_obj)
        routes_list = self._make_route_table_routes_list(rt_obj.routes)
        return self._make_route_table_routes_info(
            id, rt_obj.project_id, routes_list)

    def remove_route_table_routes(self, context, id, route_table):
        rt = route_table[apidef.RESOURCE_NAME]
        routes = rt['routes']
        rt_obj = self._get_route_table(context, id)

        try:
            with db_api.context_manager.writer.using(context):
                for route in routes:
                    routes_obj = route_table_obj.RouteTableRoutes.get_object(
                        context,
                        routetable_id=id,
                        destination=route['destination'])
                    if routes_obj:
                        routes_obj.delete()
                rt_obj.update()
        except Exception as e:
            raise rt_exc.FailedToCreateOrUpdateRouteTable(error=e)
        self.driver.update_route_table(context, rt_obj)
        routes_list = self._make_route_table_routes_list(rt_obj.routes)
        return self._make_route_table_routes_info(
            id, rt_obj.project_id, routes_list)

    def sync_route_table_info(self, context, routers):
        if not routers:
            return

        for router in routers:
            route_tables = route_table_obj.RouteTable.get_objects(
                context, router_id=router['id'])
            router['route_tables'] = {rt['id']: rt['table_id']
                                      for rt in route_tables}
            default_rt_id = self._get_default_rt_id(context, router['id'])
            router['default_route_table'] = default_rt_id

            filters = {"routetable_id": [rt['id'] for rt in route_tables]}
            route_table_routes = self.get_route_table_routes(
                context, filters=filters)
            router['route_table_routes'] = route_table_routes

            binding_subnets = self.get_associated_subnets(
                context, filters=filters)
            for binding in binding_subnets:
                cidr = self._core_plugin.get_subnet(
                    context, id=binding['subnet_id'])['cidr']
                binding.update({'cidr': cidr})
            router['route_table_subnet_bindings'] = binding_subnets
