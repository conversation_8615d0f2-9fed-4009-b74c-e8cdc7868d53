# Copyright 2023 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""Create L3HARouterNetwork.project_id unique constraint

Revision ID: 682c319773d7
Revises: b1bca967e19d
Create Date: 2023-04-27 13:45:05.103963

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '682c319773d7'
down_revision = 'b1bca967e19d'


def upgrade():
    TABLE = 'ha_router_networks'
    COLUMN = 'project_id'
    exist_unique_constraints = False
    unique_name = 'uniq_%s0%s' % (TABLE, COLUMN)

    inspect = sa.engine.reflection.Inspector.from_engine(op.get_bind())
    # fullstack test run this upgrade migration first, but the column
    # 'project_id' of table 'ha_router_networks' is still 'tenant_id'
    # in the database. So we need to check the column name first.
    for column in inspect.get_columns(TABLE):
        if column['name'] == 'tenant_id':
            COLUMN = 'tenant_id'
            unique_name = 'uniq_%s0%s' % (TABLE, COLUMN)

    unique_constraints = inspect.get_unique_constraints(TABLE)
    for unique_constraint in unique_constraints:
        if unique_constraint['name'] == unique_name:
            exist_unique_constraints = True

    if not exist_unique_constraints:
        op.create_unique_constraint(
            constraint_name=unique_name,
            table_name=TABLE,
            columns=[COLUMN])
