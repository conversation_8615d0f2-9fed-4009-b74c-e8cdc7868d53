# Copyright 2021 OpenStack Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.
#

from neutron_lib.api.definitions import l3 as l3_apidef
from neutron_lib.callbacks import events
from neutron_lib.callbacks import registry
from neutron_lib.callbacks import resources
from neutron_lib import exceptions
from oslo_log import log as logging

from neutron._i18n import _

LOG = logging.getLogger(__name__)
from neutron.common import utils
from neutron.db import _resource_extend as resource_extend
from neutron.db import l3_attrs_db
from neutron.extensions import _router_custom_type as apidef


@resource_extend.has_resource_extenders
class RouterCustomTypeMixin(l3_attrs_db.ExtraAttributesMixin):
    """Mixin class of router's custom type."""

    @staticmethod
    @resource_extend.extends([l3_apidef.ROUTERS])
    def _add_custom_type_to_response(router_res, router_db):
        custom_type = router_db['extra_attributes'].get('custom_type', '[]')
        router_res['custom_type'] = (
            utils.convert_string_to_list(custom_type))

    def validate_custom_type(self, custom_type):
        ct_string = utils.convert_list_to_string(custom_type)
        if len(ct_string) > apidef.MAX_CUSTOM_TYPE_LEN:
            msg = _("Too many custom_type specified")
            raise exceptions.InvalidInput(error_message=msg)

    @registry.receives(resources.ROUTER, [events.PRECOMMIT_CREATE])
    def _process_custom_type_create(self, resource, event, trigger, context,
                                    router, router_db, **kwargs):
        # Reentry prevention: Check if custom_type already set
        if (hasattr(router_db, 'extra_attributes') and
            router_db.extra_attributes and
            router_db.extra_attributes.custom_type is not None):
            LOG.debug("Custom type already set for router %s, skipping", router_db.id)
            return

        custom_type = router.get('custom_type', [])
        self.validate_custom_type(custom_type)
        self.set_extra_attr_value(
            context, router_db, 'custom_type', custom_type)

    @registry.receives(resources.ROUTER, [events.PRECOMMIT_UPDATE])
    def _process_custom_type_update(self, resource, event, trigger,
                                    payload=None):
        if not payload:
            return

        if 'custom_type' not in payload.request_body:
            return

        # Reentry prevention: Check if custom_type already updated
        requested_custom_type = payload.request_body.get('custom_type')
        current_custom_type = None
        if (payload.desired_state.extra_attributes and
            payload.desired_state.extra_attributes.custom_type is not None):
            current_custom_type = utils.convert_string_to_list(
                payload.desired_state.extra_attributes.custom_type)

        if current_custom_type == requested_custom_type:
            LOG.debug("Custom type already updated for router %s, skipping",
                     payload.resource_id)
            return

        custom_type = requested_custom_type
        self.validate_custom_type(custom_type)
        self.set_extra_attr_value(payload.context, payload.desired_state,
                                  'custom_type', custom_type)
