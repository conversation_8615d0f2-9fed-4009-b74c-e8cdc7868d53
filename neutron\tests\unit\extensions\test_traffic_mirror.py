#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import mock

from neutron_lib import context
from neutron_lib.plugins import directory
from neutron_lib.plugins.ml2 import api
from oslo_config import cfg
from oslo_utils import uuidutils

from neutron.common import utils
from neutron.db import api as db_api
from neutron.extensions import _traffic_mirror as api_def
from neutron.extensions import traffic_mirror as traffic_mirror_ext
from neutron.objects.plugins.ml2 import vxlanallocation as vxlan_alloc_obj
from neutron.objects import traffic_mirror
from neutron.services.traffic_mirror.common import exceptions as tm_exc
from neutron.services.traffic_mirror import plugin
from neutron.tests.unit.db import test_db_base_plugin_v2

_uuid = uuidutils.generate_uuid


class TestTrafficMirrorPlugin(plugin.TrafficMirrorPlugin):
    supported_extension_aliases = [api_def.ALIAS]


class TestTrafficMirrorExtensionManager(object):
    def get_resources(self):
        return traffic_mirror_ext.Traffic_mirror.get_resources()

    def get_actions(self):
        return []

    def get_request_extensions(self):
        return []


class TestTrafficMirrorExtension(
        test_db_base_plugin_v2.NeutronDbPluginV2TestCase):
    def setUp(self):
        cfg.CONF.set_override('vni_ranges', '1:20', group='ml2_type_vxlan')

        svc_plugins = ('neutron.tests.unit.extensions.test_traffic_mirror.'
                       'TestTrafficMirrorPlugin',)
        ext_mgr = TestTrafficMirrorExtensionManager()
        super(TestTrafficMirrorExtension, self).setUp(
            plugin='neutron.plugins.ml2.plugin.Ml2Plugin',
            ext_mgr=ext_mgr, service_plugins=svc_plugins)
        self.tm_plugin = directory.get_plugin(api_def.TRAFFIC_MIRROR)
        self.tm_plugin.push_api = mock.Mock()

        self.ctx = context.get_admin_context()
        self.tennat_id = _uuid()
        self.tmf = self._create_traffic_mirror_filter()
        self.ingress_rule_1 = {
                'traffic_mirror_filter_id': self.tmf['id'],
                'ethertype': 'IPv4',
                'src_cidr': '***********/24',
                'dst_cidr': '***********/24',
                'src_port_range': '1024:2048',
                'dst_port_range': '1024:2048',
                'protocol': 'all',
                'direction': 'ingress',
                'action': 'accept', 'priority': 90}
        self.ingress_rule_2 = {
                'traffic_mirror_filter_id': self.tmf['id'],
                'ethertype': 'IPv4',
                'src_cidr': '***********/24',
                'direction': 'ingress',
                'action': 'reject', 'priority': 30}
        self.egress_rule_1 = {
                'traffic_mirror_filter_id': self.tmf['id'],
                'ethertype': 'IPv4',
                'direction': 'egress',
                'action': 'accept', 'priority': 90}
        self.egress_rule_2 = {
                'traffic_mirror_filter_id': self.tmf['id'],
                'dst_cidr': '***********/24',
                'dst_port_range': '1024:2048',
                'ethertype': 'IPv4',
                'direction': 'egress',
                'action': 'reject', 'priority': 50}

    def _create_traffic_mirror_filter(self, name=None):
        name = name or 'test_filter'
        traffic_filter = {api_def.TRAFFIC_MIRROR_FILTER: {
            'project_id': self.tennat_id,
            'name': name
        }}
        return self.tm_plugin.create_traffic_mirror_filter(self.ctx,
                                                           traffic_filter)

    def _create_traffic_mirror_filter_rule(self,
                                           traffic_mirror_filter_id=None,
                                           ethertype='IPv4',
                                           direction='ingress',
                                           action='accept',
                                           priority=10,
                                           **kwargs):
        traffic_mirror_filter_id = traffic_mirror_filter_id or self.tmf['id']
        rule = {api_def.TRAFFIC_MIRROR_FILTER_RULE: {
            'project_id': self.tennat_id,
            'traffic_mirror_filter_id': traffic_mirror_filter_id,
            'ethertype': ethertype,
            'direction': direction,
            'action': action,
            'priority': priority,
        }}
        rule[api_def.TRAFFIC_MIRROR_FILTER_RULE].update(**kwargs)
        return self.tm_plugin.create_traffic_mirror_filter_rule(self.ctx, rule)

    def _create_traffic_mirror_session(self, name=None,
                                       filter_id=None,
                                       sources=None,
                                       target_port_id=None,
                                       **kwargs):
        traffic_mirror_filter_id = filter_id or self.tmf['id']
        traffic_mirror_sources = sources or []
        traffic_mirror_target_port_id = target_port_id or _uuid()
        session = {api_def.TRAFFIC_MIRROR_SESSION: {
            'project_id': self.tennat_id,
            'name': name,
            'traffic_mirror_filter_id': traffic_mirror_filter_id,
            'traffic_mirror_sources': traffic_mirror_sources,
            'traffic_mirror_target_port_id': traffic_mirror_target_port_id,
            'traffic_mirror_target_type': 'eni',
            'priority': kwargs.get('priority', 10),
            'packet_length': kwargs.get('packet_length', 100),
            'enabled': kwargs.get('enabled', False),
            'virtual_network_id': kwargs.get('virtual_network_id', 1)
        }}
        return self.tm_plugin.create_traffic_mirror_session(self.ctx, session)

    def _create_db_obj_mock(self, **kwargs):
        """
        Helper method to create a properly
        configured mock database object.
        """

        db_obj_data = {
            'id': _uuid(),
            'project_id': self.tennat_id,
            'traffic_mirror_filter_id': self.tmf['id'],
            'direction': 'ingress',
            'ethertype': 'IPv4',
            'protocol': 'tcp',
            'src_cidr': '***********/24',
            'dst_cidr': '10.0.0.0/24',
            'src_port_range_min': 1024,
            'src_port_range_max': 2048,
            'dst_port_range_min': 1024,
            'dst_port_range_max': 2048,
            'action': 'accept',
            'priority': 90
        }
        # Override with any provided kwargs
        db_obj_data.update(kwargs)

        db_obj_mock = mock.Mock()
        for key, value in db_obj_data.items():
            setattr(db_obj_mock, key, value)
        db_obj_mock.__getitem__ = lambda _, key: db_obj_data[key]
        db_obj_mock.get = lambda key, default=None: (
            db_obj_data.get(key, default))

        return db_obj_mock

    def test_create_traffic_mirror_filter(self):
        tm_filter = self._create_traffic_mirror_filter('test_filter')
        self.assertEqual('test_filter', tm_filter['name'])
        self.assertEqual([], tm_filter['ingress_rules'])
        self.assertEqual([], tm_filter['egress_rules'])

    def test_update_traffic_mirror_filter(self):
        filter_kw = {api_def.TRAFFIC_MIRROR_FILTER: {
            'name': 'new_name',
            'description': 'new_description'
        }}
        tm_filter = self.tm_plugin.update_traffic_mirror_filter(
            self.ctx, self.tmf['id'], filter_kw)
        self.assertEqual('new_name', tm_filter['name'])
        self.assertEqual('new_description', tm_filter['description'])

    def test_get_traffic_mirror_filter(self):
        self._create_traffic_mirror_filter_rule(**self.ingress_rule_1)
        self._create_traffic_mirror_filter_rule(**self.egress_rule_1)
        tm_filter = self.tm_plugin.get_traffic_mirror_filter(
            self.ctx, self.tmf['id'])
        self.assertEqual('test_filter', tm_filter['name'])
        self.assertIsNone(tm_filter['description'])
        self.assertEqual(1, len(tm_filter['ingress_rules']))
        self.assertEqual(1, len(tm_filter['egress_rules']))

    def test_get_traffic_mirror_filters(self):
        tmf2 = self._create_traffic_mirror_filter('test_filter2')
        tmfs = self.tm_plugin.get_traffic_mirror_filters(self.ctx)
        self.assertEqual(2, len(tmfs))
        self.assertIn(self.tmf['id'], [x['id'] for x in tmfs])
        self.assertIn(tmf2['id'], [x['id'] for x in tmfs])

    def test_delete_traffic_mirror_filter(self):
        self.tm_plugin.delete_traffic_mirror_filter(self.ctx, self.tmf['id'])
        self.assertRaises(
            tm_exc.TrafficMirrorFilterNotFound,
            self.tm_plugin.get_traffic_mirror_filter,
            self.ctx, self.tmf['id'])

    def test_delete_bounded_traffic_mirror_filter(self):
        self._create_traffic_mirror_session()
        self.assertRaises(
            tm_exc.TrafficMirrorFilterInUse,
            self.tm_plugin.delete_traffic_mirror_filter,
            self.ctx, self.tmf['id'])

    def test_modify_fields_from_db_protocol_all(self):
        db_obj_mock = self._create_db_obj_mock(protocol='all')

        fields = (traffic_mirror.TrafficMirrorFilterRule
                  .modify_fields_from_db(db_obj_mock))
        self.assertIsNone(fields['protocol'])
        self.assertIsInstance(fields['src_cidr'], utils.AuthenticIPNetwork)
        self.assertEqual('***********/24', str(fields['src_cidr']))
        self.assertIsInstance(fields['dst_cidr'], utils.AuthenticIPNetwork)
        self.assertEqual('10.0.0.0/24', str(fields['dst_cidr']))

    def test_modify_fields_from_db_src_cidr_none(self):
        db_obj_mock = self._create_db_obj_mock(
            protocol='tcp',
            src_cidr=None,
            src_port_range_min=None,
            src_port_range_max=None
        )

        fields = (traffic_mirror.TrafficMirrorFilterRule
                  .modify_fields_from_db(db_obj_mock))
        self.assertEqual('tcp', fields['protocol'])
        # When src_cidr is None, it's not
        # included in the fields dict by base class
        self.assertNotIn('src_cidr', fields)
        self.assertIsInstance(fields['dst_cidr'], utils.AuthenticIPNetwork)
        self.assertEqual('10.0.0.0/24', str(fields['dst_cidr']))

    def test_modify_fields_from_db_dst_cidr_none(self):
        db_obj_mock = self._create_db_obj_mock(
            protocol='udp',
            dst_cidr=None,
            dst_port_range_min=None,
            dst_port_range_max=None
        )

        fields = (traffic_mirror.TrafficMirrorFilterRule
                  .modify_fields_from_db(db_obj_mock))
        self.assertEqual('udp', fields['protocol'])
        self.assertIsInstance(fields['src_cidr'], utils.AuthenticIPNetwork)
        self.assertEqual('***********/24', str(fields['src_cidr']))
        # When dst_cidr is None, it's not
        # included in the fields dict by base class
        self.assertNotIn('dst_cidr', fields)

    def test_get_traffic_mirror_filter_rule_with_protocol_all(self):
        """
        Test _get_traffic_mirror_filter_rule
        handles protocol='all' correctly.
        """

        # Create a rule with protocol='all' in database
        rule = self._create_traffic_mirror_filter_rule(
            protocol='all',
            src_cidr='***********/24',
            dst_cidr='10.0.0.0/24',
            direction='ingress',
            action='accept',
            priority=90
        )

        # Get the rule using _get_traffic_mirror_filter_rule
        rule_obj = self.tm_plugin._get_traffic_mirror_filter_rule(
            self.ctx, rule['id'])

        # Verify that protocol is None (converted from 'all')
        self.assertIsNone(rule_obj.protocol)
        # Verify other fields are correctly processed
        self.assertIsInstance(rule_obj.src_cidr, utils.AuthenticIPNetwork)
        self.assertEqual('***********/24', str(rule_obj.src_cidr))
        self.assertIsInstance(rule_obj.dst_cidr, utils.AuthenticIPNetwork)
        self.assertEqual('10.0.0.0/24', str(rule_obj.dst_cidr))

    def test_get_traffic_mirror_filter_rule_with_null_cidrs(self):
        """
        Test _get_traffic_mirror_filter_rule
        handles NULL CIDR fields correctly.
        """

        # Create a rule with minimal fields (no CIDR specified)
        rule = self._create_traffic_mirror_filter_rule(
            protocol='tcp',
            direction='egress',
            action='reject',
            priority=50
        )

        # Get the rule using _get_traffic_mirror_filter_rule
        rule_obj = self.tm_plugin._get_traffic_mirror_filter_rule(
            self.ctx, rule['id'])

        # Verify that CIDR fields are None when not specified
        self.assertEqual('tcp', rule_obj.protocol)
        self.assertIsNone(rule_obj.src_cidr)
        self.assertIsNone(rule_obj.dst_cidr)

    def test_get_traffic_mirror_filter_rule_with_mixed_null_cidrs(self):
        """
        Test _get_traffic_mirror_filter_rule
        handles mixed NULL/non-NULL CIDR fields.
        """

        # Create a rule with only src_cidr specified
        rule = self._create_traffic_mirror_filter_rule(
            protocol='udp',
            src_cidr='**********/16',
            direction='ingress',
            action='accept',
            priority=70
        )

        # Get the rule using _get_traffic_mirror_filter_rule
        rule_obj = self.tm_plugin._get_traffic_mirror_filter_rule(
            self.ctx, rule['id'])

        # Verify that only src_cidr is processed, dst_cidr remains None
        self.assertEqual('udp', rule_obj.protocol)
        self.assertIsInstance(rule_obj.src_cidr, utils.AuthenticIPNetwork)
        self.assertEqual('**********/16', str(rule_obj.src_cidr))
        self.assertIsNone(rule_obj.dst_cidr)

    def test_get_traffic_mirror_filter_rule_protocol_all_with_null_cidrs(self):
        """
        Test _get_traffic_mirror_filter_rule
        handles protocol='all' with NULL CIDRs.
        """

        # Create a rule with protocol='all' and no CIDR fields
        rule = self._create_traffic_mirror_filter_rule(
            protocol='all',
            direction='egress',
            action='accept',
            priority=100
        )

        # Get the rule using _get_traffic_mirror_filter_rule
        rule_obj = self.tm_plugin._get_traffic_mirror_filter_rule(
            self.ctx, rule['id'])

        # Verify all fields are correctly processed
        self.assertIsNone(rule_obj.protocol)  # 'all' converted to None
        self.assertIsNone(rule_obj.src_cidr)
        self.assertIsNone(rule_obj.dst_cidr)
        self.assertEqual('egress', rule_obj.direction)
        self.assertEqual('accept', rule_obj.action)
        self.assertEqual(100, rule_obj.priority)

    def test_get_traffic_mirror_filter_rule_not_found(self):
        """
        Test _get_traffic_mirror_filter_rule
        raises exception for non-existent rule.
        """

        non_existent_id = _uuid()

        # Verify that exception is raised for non-existent rule
        self.assertRaises(
            tm_exc.TrafficMirrorFilterRuleNotFound,
            self.tm_plugin._get_traffic_mirror_filter_rule,
            self.ctx, non_existent_id
        )

    def test_get_traffic_mirror_filter_rule_api_protocol_all_conversion(self):
        """
        Test public API correctly converts
        protocol=None back to 'all' for display.
        """

        # Create a rule with protocol='all'
        rule = self._create_traffic_mirror_filter_rule(
            protocol='all',
            src_cidr='***********/24',
            dst_cidr='10.0.0.0/24',
            direction='ingress',
            action='accept',
            priority=90
        )

        # Get the rule using public API
        result = self.tm_plugin.get_traffic_mirror_filter_rule(
            self.ctx, rule['id'])

        # Verify that protocol is converted back to 'all' for API response
        self.assertEqual('all', result['protocol'])
        # Verify CIDR fields are properly formatted as strings
        self.assertEqual('***********/24', result['src_cidr'])
        self.assertEqual('10.0.0.0/24', result['dst_cidr'])

    def test_get_traffic_mirror_filter_rule_api_null_cidrs_display(self):
        """Test public API correctly displays NULL CIDR fields."""

        # Create a rule with no CIDR fields
        rule = self._create_traffic_mirror_filter_rule(
            protocol='tcp',
            direction='egress',
            action='reject',
            priority=50
        )

        # Get the rule using public API
        result = self.tm_plugin.get_traffic_mirror_filter_rule(
            self.ctx, rule['id'])

        # Verify that CIDR fields are displayed as 'None' strings
        self.assertEqual('tcp', result['protocol'])
        self.assertEqual('None', result['src_cidr'])
        self.assertEqual('None', result['dst_cidr'])

    def test_modify_fields_from_db_both_cidrs_none(self):
        """Test modify_fields_from_db handles both CIDR fields as None."""

        db_obj_mock = self._create_db_obj_mock(
            protocol='all',
            src_cidr=None,
            dst_cidr=None,
            src_port_range_min=None,
            src_port_range_max=None,
            dst_port_range_min=None,
            dst_port_range_max=None
        )

        fields = (traffic_mirror.TrafficMirrorFilterRule
                  .modify_fields_from_db(db_obj_mock))

        # Verify protocol 'all' is converted to None
        self.assertIsNone(fields['protocol'])
        # Verify both CIDR fields are not
        # included when None (base class behavior)
        self.assertNotIn('src_cidr', fields)
        self.assertNotIn('dst_cidr', fields)

    def test_modify_fields_from_db_protocol_not_all(self):
        """
        Test modify_fields_from_db doesn't
        modify non-'all' protocol values.
        """

        protocols_to_test = ['tcp', 'udp', 'icmp', 'icmpv6', '6', '17', '1']

        for protocol in protocols_to_test:
            db_obj_mock = self._create_db_obj_mock(protocol=protocol)

            fields = (traffic_mirror.TrafficMirrorFilterRule
                      .modify_fields_from_db(db_obj_mock))

            # Verify protocol is unchanged for non-'all' values
            self.assertEqual(protocol, fields['protocol'],
                           "Protocol %s should remain unchanged" % protocol)
            # Verify CIDR fields are processed correctly
            self.assertIsInstance(fields['src_cidr'], utils.AuthenticIPNetwork)
            self.assertIsInstance(fields['dst_cidr'], utils.AuthenticIPNetwork)

    def test_get_traffic_mirror_filter_rule_integration_with_various_protocols(
            self):
        """
        Integration test for _get_traffic_mirror_filter_rule
        with various protocol values.
        """

        test_cases = [
            {'protocol': 'all',
             'expected_internal': None,
             'expected_api': 'all'},
            {'protocol': 'tcp',
             'expected_internal': 'tcp',
             'expected_api': 'tcp'},
            {'protocol': 'udp',
             'expected_internal': 'udp',
             'expected_api': 'udp'},
            {'protocol': 'icmp',
             'expected_internal': 'icmp',
             'expected_api': 'icmp'},
        ]

        for case in test_cases:
            # Create rule with specific protocol
            rule = self._create_traffic_mirror_filter_rule(
                protocol=case['protocol'],
                src_cidr='***********/24',
                direction='ingress',
                action='accept',
                priority=90
            )

            # Test internal object representation
            rule_obj = self.tm_plugin._get_traffic_mirror_filter_rule(
                self.ctx, rule['id'])
            self.assertEqual(case['expected_internal'], rule_obj.protocol,
                           "Internal protocol for %s should be %s" % (
                               case['protocol'], case['expected_internal']))

            # Test API representation
            api_result = self.tm_plugin.get_traffic_mirror_filter_rule(
                self.ctx, rule['id'])
            self.assertEqual(case['expected_api'], api_result['protocol'],
                           "API protocol for %s should be %s" % (
                               case['protocol'], case['expected_api']))

            # Clean up
            self.tm_plugin.delete_traffic_mirror_filter_rule(
                self.ctx, rule['id'])

    def test_create_traffic_mirror_filter_rule(self):
        rule = self._create_traffic_mirror_filter_rule(**self.ingress_rule_1)
        self.assertEqual('accept', rule['action'])
        self.assertEqual(90, rule['priority'])
        self.assertEqual(self.tmf['id'], rule['traffic_mirror_filter_id'])
        self.assertEqual('ingress', rule['direction'])
        self.assertEqual('IPv4', rule['ethertype'])
        self.assertEqual('all', rule['protocol'])
        self.assertEqual('***********/24', rule['src_cidr'])
        self.assertEqual('***********/24', rule['dst_cidr'])
        self.assertEqual('1024:2048', rule['src_port_range'])
        self.assertEqual('1024:2048', rule['dst_port_range'])

    def test_create_traffic_mirror_filter_duplicate_rule(self):
        rule1 = self._create_traffic_mirror_filter_rule(**self.egress_rule_1)
        self.assertEqual('egress', rule1['direction'])
        self.assertRaises(tm_exc.TrafficMirrorFilterRuleDuplicateOrConflict,
                          self._create_traffic_mirror_filter_rule,
                          **self.egress_rule_1)

    def test_create_traffic_mirror_filter_rule_exceed_limits(self):
        cfg.CONF.set_override(
            'max_ingress_rule_per_traffic_mirror_filter', 1, 'traffic_mirror')
        cfg.CONF.set_override(
            'max_egress_rule_per_traffic_mirror_filter', 1, 'traffic_mirror')
        self.tm_plugin.max_ingress_rules = 1
        self.tm_plugin.max_egress_rules = 1

        self._create_traffic_mirror_filter_rule(**self.ingress_rule_1)
        self.assertRaises(tm_exc.TrafficMirrorFilterRuleLimitExceeded,
                          self._create_traffic_mirror_filter_rule,
                          **self.ingress_rule_2)

        self._create_traffic_mirror_filter_rule(**self.egress_rule_1)
        self.assertRaises(tm_exc.TrafficMirrorFilterRuleLimitExceeded,
                          self._create_traffic_mirror_filter_rule,
                          **self.egress_rule_2)

    def test_create_traffic_mirror_filter_rule_invalid(self):
        # invalid port value
        self.assertRaises(tm_exc.TrafficMirrorFilterRuleInvalidPortValue,
                          self._create_traffic_mirror_filter_rule,
                          src_port_range='0:100')

        self.assertRaises(tm_exc.TrafficMirrorFilterRuleInvalidPortValue,
                          self._create_traffic_mirror_filter_rule,
                          dst_port_range='0:100')

        # invalid port range
        self.assertRaises(tm_exc.TrafficMirrorFilterRuleInvalidPortRange,
                          self._create_traffic_mirror_filter_rule,
                          src_port_range='100:10')

        self.assertRaises(tm_exc.TrafficMirrorFilterRuleInvalidPortRange,
                          self._create_traffic_mirror_filter_rule,
                          dst_port_range='100:10')

        # invalid cidr with ethertype
        self.assertRaises(tm_exc.TrafficMirrorFilterRuleInvalid,
                          self._create_traffic_mirror_filter_rule,
                          src_cidr='***********/24', ethertype='IPv6')

        self.assertRaises(tm_exc.TrafficMirrorFilterRuleInvalid,
                          self._create_traffic_mirror_filter_rule,
                          dst_cidr='***********/24', ethertype='IPv6')

        # # invalid protocol with ethertype
        self.assertRaises(tm_exc.TrafficMirrorFilterRuleInvalid,
                          self._create_traffic_mirror_filter_rule,
                          protocol='icmpv6', ethertype='IPv4')

    def test_update_traffic_mirror_filter_rule(self):
        rule = self._create_traffic_mirror_filter_rule(**self.ingress_rule_1)
        update = {api_def.TRAFFIC_MIRROR_FILTER_RULE: {
            'protocol': 'tcp',
            'src_port_range': '1000:3000',
        }}
        rule = self.tm_plugin.update_traffic_mirror_filter_rule(
            self.ctx, rule['id'], update)
        self.assertEqual('tcp', rule['protocol'])
        self.assertEqual('1000:3000', rule['src_port_range'])

    def test_update_traffic_mirror_filter_rule_action(self):
        rule = self._create_traffic_mirror_filter_rule(**self.ingress_rule_1)
        update = {api_def.TRAFFIC_MIRROR_FILTER_RULE: {
            'action': 'reject',
        }}
        rule = self.tm_plugin.update_traffic_mirror_filter_rule(
            self.ctx, rule['id'], update)
        self.assertEqual('reject', rule['action'])

    def test_update_traffic_mirror_filter_rule_port_range(self):
        rule = self._create_traffic_mirror_filter_rule(**self.ingress_rule_1)
        update = {api_def.TRAFFIC_MIRROR_FILTER_RULE: {
            'src_port_range': '1000:3000',
        }}
        rule = self.tm_plugin.update_traffic_mirror_filter_rule(
            self.ctx, rule['id'], update)
        self.assertEqual('1000:3000', rule['src_port_range'])

        update = {api_def.TRAFFIC_MIRROR_FILTER_RULE: {
            'dst_port_range': '2000:3000',
        }}
        rule = self.tm_plugin.update_traffic_mirror_filter_rule(
            self.ctx, rule['id'], update)
        self.assertEqual('2000:3000', rule['dst_port_range'])

    def test_update_traffic_mirror_filter_rule_desc(self):
        rule = self._create_traffic_mirror_filter_rule(**self.ingress_rule_1)
        update = {api_def.TRAFFIC_MIRROR_FILTER_RULE: {
            'description': '1234',
        }}
        rule = self.tm_plugin.update_traffic_mirror_filter_rule(
            self.ctx, rule['id'], update)
        self.assertEqual('1234', rule['description'])

    def test_update_traffic_mirror_filter_rule_invalid(self):
        rule = self._create_traffic_mirror_filter_rule(**self.ingress_rule_1)
        update = {api_def.TRAFFIC_MIRROR_FILTER_RULE: {
            'protocol': 'icmpv6'}}
        self.assertRaises(tm_exc.TrafficMirrorFilterRuleInvalid,
                          self.tm_plugin.update_traffic_mirror_filter_rule,
                          self.ctx, rule['id'], update)

    def test_update_traffic_mirror_filter_rule_duplicate(self):
        rule1 = self._create_traffic_mirror_filter_rule(**self.egress_rule_1)
        self._create_traffic_mirror_filter_rule(**self.ingress_rule_2)
        update = {api_def.TRAFFIC_MIRROR_FILTER_RULE: {
            'ethertype': 'IPv4',
            'src_cidr': '***********/24',
            'direction': 'ingress',
            'action': 'reject', 'priority': 30}
        }
        self.assertRaises(tm_exc.TrafficMirrorFilterRuleDuplicateOrConflict,
                          self.tm_plugin.update_traffic_mirror_filter_rule,
                          self.ctx, rule1['id'], update)

    def test_get_traffic_mirror_filter_rule(self):
        rule = self._create_traffic_mirror_filter_rule(**self.ingress_rule_1)
        result = self.tm_plugin.get_traffic_mirror_filter_rule(
            self.ctx, rule['id'])
        self.assertEqual(rule['traffic_mirror_filter_id'],
                         result['traffic_mirror_filter_id'])
        self.assertEqual(rule['id'], result['id'])
        self.assertEqual(rule['action'], result['action'])
        self.assertEqual(rule['priority'], result['priority'])
        self.assertEqual(rule['protocol'], result['protocol'])
        self.assertEqual(rule['direction'], result['direction'])
        self.assertEqual(rule['src_cidr'], result['src_cidr'])
        self.assertEqual(rule['dst_cidr'], result['dst_cidr'])
        self.assertEqual(rule['src_port_range'], result['src_port_range'])
        self.assertEqual(rule['dst_port_range'], result['dst_port_range'])

    def test_get_traffic_mirror_filter_rules(self):
        rule1 = self._create_traffic_mirror_filter_rule(**self.ingress_rule_1)
        rule2 = self._create_traffic_mirror_filter_rule(**self.egress_rule_1)
        rules = self.tm_plugin.get_traffic_mirror_filter_rules(self.ctx)
        self.assertEqual(2, len(rules))
        self.assertIn(rule1['id'], [r['id'] for r in rules])
        self.assertIn(rule2['id'], [r['id'] for r in rules])

    def test_delete_traffic_mirror_filter_rule(self):
        rule = self._create_traffic_mirror_filter_rule(**self.ingress_rule_1)
        self.tm_plugin.delete_traffic_mirror_filter_rule(self.ctx, rule['id'])
        self.assertRaises(tm_exc.TrafficMirrorFilterRuleNotFound,
                          self.tm_plugin.get_traffic_mirror_filter_rule,
                          self.ctx, rule['id'])

    @db_api.context_manager.reader
    def _get_allocation(self, context, segment):
        return vxlan_alloc_obj.VxlanAllocation.get_object(
            context, vxlan_vni=segment[api.SEGMENTATION_ID])

    def test_create_traffic_mirror_session(self):
        sources = [_uuid() for _ in range(3)]
        target_id = _uuid()
        session = self._create_traffic_mirror_session(name='session',
                                                      filter_id=self.tmf['id'],
                                                      sources=sources,
                                                      target_port_id=target_id)
        self.assertEqual(session['name'], 'session')
        self.assertEqual(session['traffic_mirror_filter_id'], self.tmf['id'])
        self.assertItemsEqual(sources, session['traffic_mirror_sources'])
        self.assertIsNotNone(session['segmentation_id'])

        segment = {api.NETWORK_TYPE: 'vxlan',
                   api.SEGMENTATION_ID: session['segmentation_id']}
        self.assertTrue(self._get_allocation(self.ctx, segment).allocated)

    def test_create_traffic_mirror_session_no_vni(self):
        vxlan_alloc_cls = vxlan_alloc_obj.VxlanAllocation
        segment = vxlan_alloc_cls.get_random_unallocated_segment(
            self.ctx)
        # allocate all vxlan's vni
        while segment:
            vxlan_alloc_obj.VxlanAllocation.allocate(
                self.ctx, vxlan_vni=segment['vxlan_vni'])
            segment = vxlan_alloc_cls.get_random_unallocated_segment(
                self.ctx)
        self.assertRaisesRegex(tm_exc.TrafficMirrorSessionCreateError,
                               'No free VXLAN segments available',
                               self._create_traffic_mirror_session)

    def test_create_traffic_mirror_session_exceed_limit(self):
        cfg.CONF.set_override(
            'max_source_per_traffic_mirror_session', 2, 'traffic_mirror')
        cfg.CONF.set_override(
            'max_traffic_mirror_session_per_source', 1, 'traffic_mirror')
        self.tm_plugin.max_sources = 2
        self.tm_plugin.max_sessions = 1

        # traffic session sources exceed
        sources = [_uuid() for _ in range(3)]
        self.assertRaises(tm_exc.TrafficMirrorSessionLimitExceeded,
                          self._create_traffic_mirror_session,
                          sources=sources)

        # source bound session exceed
        source_id = _uuid()
        session = self._create_traffic_mirror_session(sources=[source_id])
        self.assertIn(source_id, session['traffic_mirror_sources'])
        self.assertRaises(tm_exc.TrafficMirrorSessionLimitExceeded,
                          self._create_traffic_mirror_session,
                          sources=[source_id])

    def test_create_traffic_mirror_session_same_source_target(self):
        sources = [_uuid() for _ in range(2)]
        target = sources[0]
        self.assertRaises(tm_exc.TrafficMirrorSessionSourcesTargetConflict,
                          self._create_traffic_mirror_session,
                          sources=sources, target_port_id=target)

    def test_create_traffic_mirror_session_conflict_source_target(self):
        sources1 = [_uuid() for _ in range(2)]
        sources2 = [_uuid() for _ in range(2)]
        target1 = _uuid()
        self._create_traffic_mirror_session(sources=sources1,
                                            target_port_id=target1)
        # target as source
        self.assertRaises(tm_exc.TrafficMirrorSessionSourcesTargetConflict,
                          self._create_traffic_mirror_session,
                          sources=[target1], target_port_id=_uuid())
        # source as target
        self.assertRaises(tm_exc.TrafficMirrorSessionSourcesTargetConflict,
                          self._create_traffic_mirror_session,
                          sources=sources2, target_port_id=sources1[1])

    def test_update_traffic_mirror_session_target(self):
        target_id = _uuid()
        new_target = _uuid()
        session = self._create_traffic_mirror_session(target_port_id=target_id)
        self.assertEqual(target_id, session['traffic_mirror_target_port_id'])

        update = {
            api_def.TRAFFIC_MIRROR_SESSION:
                 {'traffic_mirror_target_port_id': new_target}
        }
        session = self.tm_plugin.update_traffic_mirror_session(
            self.ctx, session['id'], update)
        self.assertEqual(new_target, session['traffic_mirror_target_port_id'])

    def test_update_traffic_mirror_session_sources(self):
        sources = [_uuid() for _ in range(3)]
        new_sources = [_uuid() for _ in range(3)]
        session = self._create_traffic_mirror_session(sources=sources)
        self.assertItemsEqual(sources, session['traffic_mirror_sources'])

        update = {
            api_def.TRAFFIC_MIRROR_SESSION:
                 {'traffic_mirror_sources': new_sources}
        }
        session = self.tm_plugin.update_traffic_mirror_session(
            self.ctx, session['id'], update)
        self.assertItemsEqual(new_sources, session['traffic_mirror_sources'])

    def test_update_traffic_mirror_session_add_remove_sources(self):
        sources = [_uuid() for _ in range(3)]
        new_sources = sources + [_uuid() for _ in range(3)]
        session = self._create_traffic_mirror_session(sources=sources)
        self.assertItemsEqual(sources, session['traffic_mirror_sources'])

        update = {
            api_def.TRAFFIC_MIRROR_SESSION:
                 {'traffic_mirror_sources': new_sources}
        }
        session = self.tm_plugin.update_traffic_mirror_session(
            self.ctx, session['id'], update)
        self.assertItemsEqual(new_sources, session['traffic_mirror_sources'])

        update = {
            api_def.TRAFFIC_MIRROR_SESSION:
                 {'traffic_mirror_sources': sources}
        }
        session = self.tm_plugin.update_traffic_mirror_session(
            self.ctx, session['id'], update)
        self.assertItemsEqual(sources, session['traffic_mirror_sources'])

    def test_update_traffic_mirror_session_exceed_limit(self):
        cfg.CONF.set_override(
            'max_source_per_traffic_mirror_session', 2, 'traffic_mirror')
        cfg.CONF.set_override(
            'max_traffic_mirror_session_per_source', 1, 'traffic_mirror')
        self.tm_plugin.max_sources = 2
        self.tm_plugin.max_sessions = 1

        sources = [_uuid() for _ in range(2)]
        session = self._create_traffic_mirror_session(sources=sources)
        self.assertItemsEqual(sources, session['traffic_mirror_sources'])

        # traffic session sources exceed
        update = {
            api_def.TRAFFIC_MIRROR_SESSION:
                {'traffic_mirror_sources': [_uuid() for _ in range(3)]}
        }
        self.assertRaises(tm_exc.TrafficMirrorSessionLimitExceeded,
                          self.tm_plugin.update_traffic_mirror_session,
                          self.ctx, session['id'], update)

        # source bound session exceed
        new_sources = [_uuid() for _ in range(2)]
        self._create_traffic_mirror_session(sources=new_sources)
        update = {
            api_def.TRAFFIC_MIRROR_SESSION:
                {'traffic_mirror_sources': [new_sources[0]]}
        }
        self.assertRaises(tm_exc.TrafficMirrorSessionLimitExceeded,
                          self.tm_plugin.update_traffic_mirror_session,
                          self.ctx, session['id'], update)

    def test_update_traffic_mirror_session_same_source_target(self):
        sources = [_uuid() for _ in range(2)]
        target = _uuid()
        session = self._create_traffic_mirror_session(sources=sources,
                                                      target_port_id=target)
        update = {api_def.TRAFFIC_MIRROR_SESSION: {
            'traffic_mirror_target_port_id': sources[0]
        }}
        self.assertRaises(tm_exc.TrafficMirrorSessionSourcesTargetConflict,
                          self.tm_plugin.update_traffic_mirror_session,
                          self.ctx, session['id'], update)

        update = {api_def.TRAFFIC_MIRROR_SESSION: {
            'traffic_mirror_sources': [target]
        }}
        self.assertRaises(tm_exc.TrafficMirrorSessionSourcesTargetConflict,
                          self.tm_plugin.update_traffic_mirror_session,
                          self.ctx, session['id'], update)

    def test_update_traffic_mirror_session_conflict_source_target(self):
        sources1 = [_uuid() for _ in range(2)]
        sources2 = [_uuid() for _ in range(2)]
        target1 = _uuid()
        target2 = _uuid()
        session1 = self._create_traffic_mirror_session(sources=sources1,
                                                       target_port_id=target1)
        self._create_traffic_mirror_session(sources=sources2,
                                            target_port_id=target2)

        # target as source
        update = {api_def.TRAFFIC_MIRROR_SESSION: {
            'traffic_mirror_sources': [target2]
        }}
        self.assertRaises(tm_exc.TrafficMirrorSessionSourcesTargetConflict,
                          self.tm_plugin.update_traffic_mirror_session,
                          self.ctx, session1['id'], update)
        # source as target
        update = {api_def.TRAFFIC_MIRROR_SESSION: {
            'traffic_mirror_target_port_id': sources2[0]
        }}
        self.assertRaises(tm_exc.TrafficMirrorSessionSourcesTargetConflict,
                          self.tm_plugin.update_traffic_mirror_session,
                          self.ctx, session1['id'], update)

    def test_get_traffic_mirror_session(self):
        sources = [_uuid() for _ in range(2)]
        target_id = _uuid()
        priority = 100
        virtual_network_id = 10000
        packet_length = 100
        enabled = True
        name = 'test'
        session = self._create_traffic_mirror_session(
            name=name, filter_id=self.tmf['id'],
            sources=sources, target_port_id=target_id,
            priority=priority, virtual_network_id=virtual_network_id,
            packet_length=packet_length, enabled=enabled)

        result = self.tm_plugin.get_traffic_mirror_session(
            self.ctx, session['id'])
        self.assertEqual(name, result['name'])
        self.assertEqual(self.tmf['id'], result['traffic_mirror_filter_id'])
        self.assertItemsEqual(sources, result['traffic_mirror_sources'])
        self.assertEqual(target_id, result['traffic_mirror_target_port_id'])
        self.assertEqual(priority, result['priority'])
        self.assertEqual(virtual_network_id, result['virtual_network_id'])
        self.assertEqual(packet_length, result['packet_length'])
        self.assertEqual(enabled, result['enabled'])

    def test_get_traffic_mirror_sessions(self):
        session1 = self._create_traffic_mirror_session()
        session2 = self._create_traffic_mirror_session()
        result = self.tm_plugin.get_traffic_mirror_sessions(self.ctx)
        self.assertEqual(2, len(result))
        self.assertIn(session1['id'], [s['id']for s in result])
        self.assertIn(session2['id'], [s['id']for s in result])

    def test_delete_traffic_mirror_session(self):
        session = self._create_traffic_mirror_session()
        segment = {api.NETWORK_TYPE: 'vxlan',
                   api.SEGMENTATION_ID: session['segmentation_id']}
        self.assertTrue(self._get_allocation(self.ctx, segment).allocated)

        self.tm_plugin.delete_traffic_mirror_session(self.ctx, session['id'])
        self.assertFalse(self._get_allocation(self.ctx, segment).allocated)
        self.assertRaises(tm_exc.TrafficMirrorSessionNotFound,
                          self.tm_plugin.get_traffic_mirror_session,
                          self.ctx, session['id'])
