# Copyright 2021 OpenStack Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.
#

from neutron_lib.callbacks import events
from neutron_lib.callbacks import registry
from neutron_lib.callbacks import resources

from oslo_log import log as logging

from neutron.db import l3_attrs_db
from neutron.extensions.cloud_attribute import validate_cloud_attributes

LOG = logging.getLogger(__name__)


@registry.has_registry_receivers
class RouterCloudAttributesMixin(l3_attrs_db.ExtraAttributesMixin):
    """Mixin class of router's cloud_attributes."""

    @registry.receives(resources.ROUTER, [events.PRECOMMIT_CREATE])
    def _process_cloud_attributes_create(self, resource, event, trigger,
                                         context, router, router_db, **kwargs):
        # Reentry prevention: Check if cloud_attributes already set
        if (hasattr(router_db, 'extra_attributes') and
            router_db.extra_attributes and
            router_db.extra_attributes.cloud_attributes is not None):
            LOG.debug("Cloud attributes already set for router %s, skipping", router_db.id)
            return

        cloud_attributes = router.get('cloud_attributes', {})
        validate_cloud_attributes(cloud_attributes)
        self.set_extra_attr_value(
            context, router_db, 'cloud_attributes', cloud_attributes)

    @registry.receives(resources.ROUTER, [events.PRECOMMIT_UPDATE])
    def _process_cloud_attributes_update(self, resource, event, trigger,
                                         payload=None):
        if not payload:
            return

        cloud_attributes = payload.request_body.get('cloud_attributes')
        if not cloud_attributes:
            return

        # Reentry prevention: Check if cloud_attributes already updated
        cur_cloud_attributes = payload.states[0].get('cloud_attributes', {})
        merged_attributes = cur_cloud_attributes.copy()
        merged_attributes.update(cloud_attributes)

        # Check if current attributes in DB already match the merged attributes
        if (payload.desired_state.extra_attributes and
            payload.desired_state.extra_attributes.cloud_attributes):
            current_db_attrs = payload.desired_state.extra_attributes.cloud_attributes
            if isinstance(current_db_attrs, str):
                import json
                current_db_attrs = json.loads(current_db_attrs)

            # Compare if all requested attributes are already set with same values
            already_updated = True
            for key, value in cloud_attributes.items():
                if key not in current_db_attrs or current_db_attrs[key] != value:
                    already_updated = False
                    break

            if already_updated:
                LOG.debug("Cloud attributes already updated for router %s, skipping",
                         payload.resource_id)
                return

        validate_cloud_attributes(cloud_attributes)
        validate_cloud_attributes(merged_attributes)
        self.set_extra_attr_value(payload.context, payload.desired_state,
                                  'cloud_attributes', merged_attributes)
