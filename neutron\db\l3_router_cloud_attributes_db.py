# Copyright 2021 OpenStack Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.
#

from neutron_lib.callbacks import events
from neutron_lib.callbacks import registry
from neutron_lib.callbacks import resources

from oslo_log import log as logging

from neutron.db import l3_attrs_db
from neutron.extensions.cloud_attribute import validate_cloud_attributes

LOG = logging.getLogger(__name__)


@registry.has_registry_receivers
class RouterCloudAttributesMixin(l3_attrs_db.ExtraAttributesMixin):
    """Mixin class of router's cloud_attributes."""

    @registry.receives(resources.ROUTER, [events.PRECOMMIT_CREATE])
    def _process_cloud_attributes_create(self, resource, event, trigger,
                                         context, router, router_db, **kwargs):
        cloud_attributes = router.get('cloud_attributes', {})
        validate_cloud_attributes(cloud_attributes)
        self.set_extra_attr_value(
            context, router_db, 'cloud_attributes', cloud_attributes)

    @registry.receives(resources.ROUTER, [events.PRECOMMIT_UPDATE])
    def _process_cloud_attributes_update(self, resource, event, trigger,
                                         payload=None):
        if not payload:
            return
        cloud_attributes = payload.request_body.get('cloud_attributes')
        if not cloud_attributes:
            return
        validate_cloud_attributes(cloud_attributes)
        cur_cloud_attributes = payload.states[0].get('cloud_attributes')
        cur_cloud_attributes.update(cloud_attributes)
        validate_cloud_attributes(cur_cloud_attributes)
        self.set_extra_attr_value(payload.context, payload.desired_state,
                                  'cloud_attributes', cur_cloud_attributes)
