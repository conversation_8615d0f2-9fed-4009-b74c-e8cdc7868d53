# Neutron浮动IP、弹性SNAT、端口转发回调函数详细清单

## 完整回调函数列表

### 1. FLOATING_IP资源类型回调函数

#### 1.1 直接FLOATING_IP事件回调

| 序号 | 函数名 | 文件路径 | 事件类型 | 功能描述 | 重入预防需求 |
|------|--------|----------|----------|----------|--------------|
| 1 | `_check_port_has_port_forwarding` | `neutron/services/portforwarding/pf_plugin.py` | BEFORE_CREATE, BEFORE_UPDATE | 检查端口是否已有端口转发规则 | ⚠️ 需要 |
| 2 | `_prevent_update_fip` | `neutron/services/elastic_snat/plugin.py` | BEFORE_UPDATE | 防止更新被弹性SNAT使用的浮动IP | ⚠️ 需要 |
| 3 | `_prevent_delete_fip` | `neutron/services/elastic_snat/plugin.py` | BEFORE_DELETE | 防止删除被弹性SNAT使用的浮动IP | ⚠️ 需要 |
| 4 | `_check_floatingip_request` | `neutron/services/portforwarding/pf_plugin.py` | PRECOMMIT_UPDATE, PRECOMMIT_DELETE | 检查浮动IP请求的有效性 | ⚠️ 需要 |
| 5 | `_create_dvr_floating_gw_port` | `neutron/db/l3_dvr_db.py` | AFTER_UPDATE | 为DVR创建浮动IP网关端口 | ⚠️ 需要 |
| 6 | `_send_nova_notification` | `neutron/notifiers/nova.py` | BEFORE_RESPONSE | 向Nova发送网络变更通知 | ✅ 不需要 |

#### 1.2 浮动IP相关的内联事件发布

| 序号 | 位置 | 文件路径 | 事件类型 | 功能描述 | 重入预防需求 |
|------|------|----------|----------|----------|--------------|
| 7 | `_create_floatingip` | `neutron/db/l3_db.py` | BEFORE_CREATE | 浮动IP创建前事件发布 | ✅ 不需要 |
| 8 | `_update_fip_assoc` | `neutron/db/l3_db.py` | AFTER_UPDATE | 浮动IP关联更新后事件发布 | ✅ 不需要 |
| 9 | `_make_ecs_ipv6_floatingip` | `neutron/db/l3_db.py` | PRECOMMIT_UPDATE | ECS IPv6浮动IP预提交更新 | ✅ 不需要 |

### 2. ELASTIC_SNAT资源类型回调函数

#### 2.1 直接ELASTIC_SNAT事件回调

| 序号 | 函数名 | 文件路径 | 事件类型 | 功能描述 | 重入预防需求 |
|------|--------|----------|----------|----------|--------------|
| 10 | `create_snat_meter` | `neutron/services/metering/metering_plugin.py` | AFTER_CREATE | 为弹性SNAT创建计量规则 | ⚠️ 需要 |
| 11 | `update_snat_meter` | `neutron/services/metering/metering_plugin.py` | AFTER_UPDATE | 更新弹性SNAT的计量规则 | ⚠️ 需要 |
| 12 | `delete_snat_meter` | `neutron/services/metering/metering_plugin.py` | AFTER_DELETE | 删除弹性SNAT的计量规则 | ⚠️ 需要 |

#### 2.2 弹性SNAT相关的内联事件发布

| 序号 | 位置 | 文件路径 | 事件类型 | 功能描述 | 重入预防需求 |
|------|------|----------|----------|----------|--------------|
| 13 | `create_elastic_snat` | `neutron/services/elastic_snat/plugin.py` | AFTER_CREATE | 弹性SNAT创建后事件发布 | ✅ 不需要 |
| 14 | `update_elastic_snat` | `neutron/services/elastic_snat/plugin.py` | AFTER_UPDATE | 弹性SNAT更新后事件发布 | ✅ 不需要 |
| 15 | `delete_elastic_snat` | `neutron/services/elastic_snat/plugin.py` | BEFORE_DELETE | 弹性SNAT删除前事件发布 | ✅ 不需要 |

### 3. PORT_FORWARDING资源类型回调函数

#### 3.1 直接PORT_FORWARDING事件回调

| 序号 | 函数名 | 文件路径 | 事件类型 | 功能描述 | 重入预防需求 |
|------|--------|----------|----------|----------|--------------|
| 16 | 端口转发计量通知 | `neutron/services/portforwarding/pf_plugin.py` | AFTER_CREATE | 为端口转发创建计量通知 | ✅ 不需要 |

### 4. 相关PORT事件回调函数

#### 4.1 PORT事件回调

| 序号 | 函数名 | 文件路径 | 事件类型 | 功能描述 | 重入预防需求 |
|------|--------|----------|----------|----------|--------------|
| 17 | `_notify_fip_port_ip_changed` | `neutron/db/l3_db.py` | AFTER_UPDATE | 通知浮动IP端口IP地址变更 | ✅ 不需要 |
| 18 | `_update_ipv6_or_gw_fip_addr` | `neutron/db/l3_db.py` | AFTER_UPDATE | 更新IPv6或网关浮动IP地址 | ⚠️ 需要 |
| 19 | `_process_port_request` | `neutron/services/portforwarding/pf_plugin.py` | AFTER_UPDATE, PRECOMMIT_DELETE | 处理端口转发相关的端口请求 | ⚠️ 需要 |
| 20 | `_get_ipv6_fip_routers_when_del_port` | `neutron/db/l3_db.py` | BEFORE_DELETE | 端口删除前获取IPv6浮动IP路由器信息 | ⚠️ 需要 |
| 21 | `_notify_ipv6_fip_routers_when_del_port` | `neutron/db/l3_db.py` | AFTER_DELETE | 端口删除后通知IPv6浮动IP路由器 | ✅ 不需要 |
| 22 | `_precommit_delete_port_callback` | `neutron/db/l3_db.py` | PRECOMMIT_DELETE | 端口删除前的预提交处理 | ✅ 不需要 |

### 5. 相关ROUTER事件回调函数

#### 5.1 ROUTER事件回调

| 序号 | 函数名 | 文件路径 | 事件类型 | 功能描述 | 重入预防需求 |
|------|--------|----------|----------|----------|--------------|
| 23 | `_prevent_update_router_gateawy` | `neutron/services/elastic_snat/plugin.py` | PRECOMMIT_UPDATE | 防止更新有弹性SNAT的路由器网关 | ⚠️ 需要 |
| 24 | `_prevent_delete_router_gateawy` | `neutron/services/elastic_snat/plugin.py` | BEFORE_DELETE | 防止删除有弹性SNAT的路由器网关 | ⚠️ 需要 |

### 6. 代理端回调函数

#### 6.1 L3代理扩展回调

| 序号 | 函数名 | 文件路径 | 事件类型 | 功能描述 | 重入预防需求 |
|------|--------|----------|----------|----------|--------------|
| 25 | `_handle_notification` | `neutron/agent/l3/extensions/port_forwarding.py` | RPC通知 | 处理端口转发RPC通知 | ✅ 不需要 |
| 26 | `process_elastic_snat` | `neutron/agent/l3/extensions/elastic_snat.py` | RPC通知 | 处理弹性SNAT配置 | ✅ 不需要 |

---

## 重入预防需求统计

### 按需求分类
- **需要重入预防**: 16个函数 (61.5%)
- **不需要重入预防**: 10个函数 (38.5%)

### 按重入预防模式分类

#### 验证条件检查模式 (8个)
1. `_check_port_has_port_forwarding`
2. `_prevent_update_fip`
3. `_prevent_delete_fip`
4. `_check_floatingip_request`
5. `_process_port_request`
6. `_prevent_update_router_gateawy`
7. `_prevent_delete_router_gateawy`
8. `_update_ipv6_or_gw_fip_addr`

#### 资源存在性检查模式 (7个)
1. `_create_dvr_floating_gw_port`
2. `create_snat_meter`
3. `update_snat_meter`
4. `delete_snat_meter`

#### 缓存状态检查模式 (1个)
1. `_get_ipv6_fip_routers_when_del_port`

### 按功能模块分类

#### 浮动IP管理 (9个需要重入预防)
- DVR浮动IP网关端口管理
- 浮动IP与端口转发集成验证
- 浮动IP与弹性SNAT集成验证
- IPv6浮动IP地址管理

#### 弹性SNAT管理 (6个需要重入预防)
- 弹性SNAT计量规则管理
- 弹性SNAT与路由器集成验证
- 弹性SNAT与浮动IP集成验证

#### 端口转发管理 (1个需要重入预防)
- 端口转发与端口状态集成处理

---

## 实现建议

### 第一阶段：核心业务逻辑保护
优先实现以下8个关键函数的重入预防：
1. `_create_dvr_floating_gw_port`
2. `_prevent_update_fip`
3. `_prevent_delete_fip`
4. `create_snat_meter`
5. `update_snat_meter`
6. `delete_snat_meter`
7. `_prevent_update_router_gateawy`
8. `_prevent_delete_router_gateawy`

### 第二阶段：验证逻辑保护
实现以下验证类函数的重入预防：
1. `_check_port_has_port_forwarding`
2. `_check_floatingip_request`
3. `_process_port_request`
4. `_update_ipv6_or_gw_fip_addr`

### 第三阶段：辅助功能保护
最后实现缓存和辅助功能的重入预防：
1. `_get_ipv6_fip_routers_when_del_port`

通过分阶段实施，可以确保最关键的业务逻辑首先得到保护，逐步建立完整的重入预防体系。
