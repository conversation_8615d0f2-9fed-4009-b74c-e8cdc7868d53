# <AUTHOR> <EMAIL>
#
# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.

import socket

import mock
from oslo_serialization import jsonutils


from neutron_lib.api.definitions import external_net as extnet_apidef
from neutron_lib.api.definitions import metering as metering_apidef
from neutron_lib import constants as lib_const
from neutron_lib import context
from neutron_lib.db import api as db_api
from neutron_lib.plugins import constants
from neutron_lib.plugins import directory
from oslo_utils import uuidutils
import webob.exc

from neutron.api.rpc.agentnotifiers import metering_rpc_agent_api
from neutron.db import l3_gwmode_db
from neutron.db.metering import metering_rpc
from neutron.extensions import _elastic_snat as api_def
from neutron.extensions import floating_ip_port_forwarding as ext_pf
from neutron.extensions import l3 as ext_l3
from neutron.extensions import metering as ext_metering
from neutron.objects import agent as agent_obj
from neutron.objects import metering
from neutron.objects import port_forwarding as pf_obj
from neutron.objects import router as l3_obj
from neutron.tests.common import helpers
from neutron.tests import tools
from neutron.tests.unit.db.metering import test_metering_db
from neutron.tests.unit.db import test_db_base_plugin_v2
from neutron.tests.unit.extensions import test_l3

_uuid = uuidutils.generate_uuid

METERING_SERVICE_PLUGIN_KLASS = (
    "neutron.services.metering."
    "metering_plugin.MeteringPlugin"
)

ROUTER_SERVICE_PLUGIN_KLASS = (
    "router"
)

FAKE_ROUTER_INFO = {
    'admin_state_up': True,
    'description': '',
    'ha': False,
    'id': 'afe9baf9-7ecf-4cc9-afed-e61949477ce1',
    'name': 'router1',
    'tenant_id': 'a7e61382-47b8-4d40-bae3-f95981b5637b',
    'project_id': 'a7e61382-47b8-4d40-bae3-f95981b5637b',
    'status': 'ACTIVE'
}

FAKE_FIP_INFO = {
    'admin_state_up': True,
    'fip_type': 'floatingip',
    'fixed_ip_address': '************',
    'id': 'afe9baf9-7ecf-4cc9-afed-e61949477ce1',
    'floating_ip_address': '***********',
    'floating_network_id': '6a39354a-d425-48fe-a365-dbb8654a4708',
    'project_id': 'a7e61382-47b8-4d40-bae3-f95981b5637b',
    'port_id': '01b3a533-6722-4bad-b683-d0cf628bbe37',
    'status': 'ACTIVE'
}


class MeteringTestExtensionManager(object):

    supported_extension_aliases = ["router", "metering", "ext-gw-mode",
                                   "l3_agent_scheduler", "port_forwarding",
                                   "elastic_snat"]

    def get_resources(self):
        l3_res = ext_l3.L3.get_resources()
        metering_res = ext_metering.Metering.get_resources()
        pf_res = ext_pf.Floating_ip_port_forwarding.get_resources()

        return l3_res + metering_res + pf_res

    def get_actions(self):
        return []

    def get_request_extensions(self):
        return []


# TODO(akamyshnikova):we need this temporary FakeContext class while Context
# checking for existence of session attribute.
class FakeContext(context.ContextBaseWithSession):
    def __init__(self, *args, **kwargs):
        super(FakeContext, self).__init__(*args, **kwargs)
        self._session = None

    @property
    def session(self):
        if self._session is None:
            self._session = db_api.get_writer_session()
        return self._session


class TestMeteringPlugin(test_db_base_plugin_v2.NeutronDbPluginV2TestCase,
                         test_l3.L3NatTestCaseMixin,
                         test_metering_db.MeteringPluginDbTestCaseMixin):

    resource_prefix_map = dict(
        (k.replace('_', '-'), "/metering")
        for k in metering_apidef.RESOURCE_ATTRIBUTE_MAP.keys()
    )

    def setUp(self):
        self.hostname = socket.gethostname()
        plugin = 'neutron.tests.unit.extensions.test_l3.TestL3NatIntPlugin'
        service_plugins = {'metering_plugin_name':
                           METERING_SERVICE_PLUGIN_KLASS
                           }
        ext_mgr = MeteringTestExtensionManager()
        super(TestMeteringPlugin, self).setUp(plugin=plugin, ext_mgr=ext_mgr,
                                              service_plugins=service_plugins)
        self.metering_plugin = directory.get_plugin(constants.METERING)

        self.uuid = '654f6b9d-0f36-4ae5-bd1b-01616794ca60'
        self.context = context.get_admin_context()

        uuid = 'oslo_utils.uuidutils.generate_uuid'
        self.uuid_patch = mock.patch(uuid, return_value=self.uuid)
        self.mock_uuid = self.uuid_patch.start()

        self.tenant_id = 'a7e61382-47b8-4d40-bae3-f95981b5637b'
        self.ctx = FakeContext('', self.tenant_id, is_admin=True)
        self.context_patch = mock.patch('neutron_lib.context.Context',
                                        return_value=self.ctx)
        self.mock_context = self.context_patch.start()

        self.topic = 'metering_agent'

        add = ('neutron.api.rpc.agentnotifiers.' +
               'metering_rpc_agent_api.MeteringAgentNotifyAPI' +
               '.add_metering_label')
        self.add_patch = mock.patch(add)
        self.mock_add = self.add_patch.start()

        remove = ('neutron.api.rpc.agentnotifiers.' +
                  'metering_rpc_agent_api.MeteringAgentNotifyAPI' +
                  '.remove_metering_label')
        self.remove_patch = mock.patch(remove)
        self.mock_remove = self.remove_patch.start()

        update = ('neutron.api.rpc.agentnotifiers.' +
                  'metering_rpc_agent_api.MeteringAgentNotifyAPI' +
                  '.update_metering_label_rules')
        self.update_patch = mock.patch(update)
        self.mock_update = self.update_patch.start()

        add_rule = ('neutron.api.rpc.agentnotifiers.' +
                    'metering_rpc_agent_api.MeteringAgentNotifyAPI' +
                    '.add_metering_label_rule')
        self.add_rule_patch = mock.patch(add_rule)
        self.mock_add_rule = self.add_rule_patch.start()

        remove_rule = ('neutron.api.rpc.agentnotifiers.' +
                       'metering_rpc_agent_api.MeteringAgentNotifyAPI' +
                       '.remove_metering_label_rule')
        self.remove_rule_patch = mock.patch(remove_rule)
        self.mock_remove_rule = self.remove_rule_patch.start()

        self.router_rev_calls = self._start_mock(
            'neutron.db.l3_db.L3_NAT_dbonly_mixin.create_router',
            return_value=FAKE_ROUTER_INFO)

    def _start_mock(self, path, return_value, new_callable=None):
        patcher = mock.patch(path, return_value=return_value,
                             new_callable=new_callable)
        patch = patcher.start()
        self.addCleanup(patcher.stop)
        return patch

    def test_routers_updated_on_host_rpc_call(self):
        router_test = {
            'id': 'xyz',
            'name': 'testrouter'}
        notify_host = ('neutron.api.rpc.agentnotifiers.' +
                       'metering_rpc_agent_api.MeteringAgentNotifyAPI' +
                       '._notification_host')
        self.notify_patch = mock.patch(notify_host)
        self.mock_notify_host = self.notify_patch.start()
        metering_rpc_handle = metering_rpc_agent_api.MeteringAgentNotifyAPI()
        metering_rpc_handle.routers_updated_on_host(
            self.ctx,
            [router_test['id']],
            'test_host')
        self.mock_notify_host.assert_called_with(self.ctx, 'routers_updated',
                                                 'test_host', routers=['xyz'])

    @mock.patch('neutron.db.l3_db.L3_NAT_dbonly_mixin.create_router')
    @mock.patch('neutron.db.l3_db.L3_NAT_dbonly_mixin.create_floatingip')
    def test_add_metering_label_rpc_call(self, router, fip):
        router.return_value = FAKE_ROUTER_INFO
        router = router.return_value
        router_id = router['id']
        fip.return_value = FAKE_FIP_INFO
        floatingip = fip.return_value
        floatingip_id = floatingip['id']
        self.admin_context = mock.Mock()
        expected = {'metering_label': {
            'description': 'desc',
            'floatingip_id': floatingip_id,
            'id': self.uuid,
            'name': 'label',
            'project_id': self.tenant_id,
            'router_id': router_id,
            'shared': False,
            'tenant_id': self.tenant_id,
            'triggeredtype': ''}}
        with self.metering_label(floatingip_id=floatingip_id,
                                 router_id=router_id,
                                 tenant_id=self.tenant_id,
                                 set_context=True) as metering_label:
            self.assertEqual(metering_label, expected)

    @mock.patch('neutron.db.l3_db.L3_NAT_dbonly_mixin.create_router')
    @mock.patch('neutron.db.l3_db.L3_NAT_dbonly_mixin.create_floatingip')
    def test_add_metering_label_shared_rpc_call(self, router, fip):
        router.return_value = FAKE_ROUTER_INFO
        router = router.return_value
        router_id = router['id']
        fip.return_value = FAKE_FIP_INFO
        floatingip = fip.return_value
        floatingip_id = floatingip['id']
        self.admin_context = mock.Mock()
        expected = {'metering_label': {
            'description': 'desc',
            'floatingip_id': floatingip_id,
            'id': self.uuid,
            'name': 'label',
            'project_id': self.tenant_id,
            'router_id': router_id,
            'shared': True,
            'tenant_id': self.tenant_id,
            'triggeredtype': ''}}

        with self.metering_label(floatingip_id=floatingip_id,
                                 router_id=router_id,
                                 tenant_id=self.tenant_id,
                                 shared=True,
                                 set_context=True) as metering_label:
            self.assertEqual(metering_label, expected)

    @mock.patch('neutron.db.l3_db.L3_NAT_dbonly_mixin.create_router')
    @mock.patch('neutron.db.l3_db.L3_NAT_dbonly_mixin.create_floatingip')
    def test_remove_metering_label_rpc_call(self, router, fip):
        router.return_value = FAKE_ROUTER_INFO
        router = router.return_value
        router_id = router['id']
        fip.return_value = FAKE_FIP_INFO
        floatingip = fip.return_value
        floatingip_id = floatingip['id']
        self.admin_context = mock.Mock()
        expected = {'metering_label': {
            'description': 'desc',
            'floatingip_id': floatingip_id,
            'id': self.uuid,
            'name': 'label',
            'project_id': self.tenant_id,
            'router_id': router_id,
            'shared': False,
            'tenant_id': self.tenant_id,
            'triggeredtype': ''}}

        with self.metering_label(floatingip_id=floatingip_id,
                                 router_id=router_id,
                                 tenant_id=self.tenant_id,
                                 set_context=True) as label:
            self.assertEqual(label, expected)
            self._delete('metering-labels',
                         label['metering_label']['id'])
        self.assertEqual(label, expected)

    @mock.patch('neutron.db.l3_db.L3_NAT_dbonly_mixin.create_router')
    @mock.patch('neutron.db.l3_db.L3_NAT_dbonly_mixin.create_floatingip')
    def test_remove_one_metering_label_rpc_call(self, router, fip):
        router.return_value = FAKE_ROUTER_INFO
        router = router.return_value
        router_id = router['id']
        fip.return_value = FAKE_FIP_INFO
        floatingip = fip.return_value
        floatingip_id = floatingip['id']
        self.admin_context = mock.Mock()

        expected_add = {'metering_label': {
            'description': 'desc',
            'floatingip_id': floatingip_id,
            'id': self.uuid,
            'name': 'label',
            'project_id': self.tenant_id,
            'router_id': router_id,
            'shared': False,
            'tenant_id': self.tenant_id,
            'triggeredtype': ''}}
        expected_remove = {'metering_label': {
            'description': 'desc',
            'floatingip_id': floatingip_id,
            'id': self.uuid,
            'name': 'label',
            'project_id': self.tenant_id,
            'router_id': router_id,
            'shared': False,
            'tenant_id': self.tenant_id,
            'triggeredtype': ''}}

        with self.metering_label(floatingip_id=floatingip_id,
                                 router_id=router_id,
                                 tenant_id=self.tenant_id,
                                 set_context=True) as label:
            self.assertEqual(label, expected_add)
            self._delete('metering-labels',
                         label['metering_label']['id'])
            self.assertEqual(label, expected_remove)

    @mock.patch('neutron.db.l3_db.L3_NAT_dbonly_mixin.create_router')
    @mock.patch('neutron.db.l3_db.L3_NAT_dbonly_mixin.create_floatingip')
    def test_add_and_remove_metering_label_rule_rpc_call(self, router, fip):
        router.return_value = FAKE_ROUTER_INFO
        router = router.return_value
        router_id = router['id']
        fip.return_value = FAKE_FIP_INFO
        floatingip = fip.return_value
        floatingip_id = floatingip['id']
        self.admin_context = mock.Mock()

        expected_add = {
            'metering_label_rule': {
                'remote_ip_prefix': '10.0.0.0/24',
                'direction': 'ingress',
                'metering_label_id': self.uuid,
                'excluded': False,
                'id': self.uuid,
                'address_group_id': None
            }
        }

        expected_del = {
            'metering_label_rule': {
                'remote_ip_prefix': '10.0.0.0/24',
                'direction': 'ingress',
                'metering_label_id': self.uuid,
                'excluded': False,
                'id': self.uuid,
                'address_group_id': None
            }
        }

        with self.metering_label(floatingip_id=floatingip_id,
                                 router_id=router_id,
                                 tenant_id=self.tenant_id,
                                 set_context=True) as label:
            la = label['metering_label']
            with self.metering_label_rule(la['id']) as label_rule:
                self.assertEqual(label_rule, expected_add)
                self._delete('metering-label-rules', self.uuid)
            self.assertEqual(label_rule, expected_del)

    @mock.patch('neutron.db.l3_db.L3_NAT_dbonly_mixin.create_router')
    @mock.patch('neutron.db.l3_db.L3_NAT_dbonly_mixin.create_floatingip')
    def test_delete_metering_label_does_not_clear_router_tenant_id(
            self, router, fip):
        router.return_value = FAKE_ROUTER_INFO
        router = router.return_value
        router_id = router['id']
        fip.return_value = FAKE_FIP_INFO
        floatingip = fip.return_value
        floatingip_id = floatingip['id']
        self.admin_context = mock.Mock()
        tenant_id = 'a7e61382-47b8-4d40-bae3-f95981b5637b'
        with self.metering_label(floatingip_id=floatingip_id,
                                 router_id=router_id,
                                 tenant_id=tenant_id) as metering_label:
            self.assertEqual(tenant_id, router['tenant_id'])
            metering_label_id = metering_label['metering_label']['id']
            self._delete('metering-labels', metering_label_id, 204)
            self.assertEqual(tenant_id, router['tenant_id'])

    @mock.patch.object(metering.MeteringLabel, 'get_object')
    def test_get_meteringlabel(self, get_object_mock):
        self.metering_plugin.get_metering_label(self.ctx, 'meter_label_id',
                                                fields=None)
        get_object_mock.assert_called_once_with(self.ctx,
                                                id='meter_label_id')

    @mock.patch.object(metering.MeteringLabel, 'get_objects')
    def test_get_meteringlabels(self, get_objects_mock):
        self.metering_plugin.get_metering_labels(self.ctx,
                                                 fields=None)
        get_objects_mock.assert_called_once_with(self.ctx,
                                                 _pager=mock.ANY)


class TestMeteringPluginL3AgentScheduler(
        test_db_base_plugin_v2.NeutronDbPluginV2TestCase,
        test_l3.L3NatTestCaseMixin,
        test_metering_db.MeteringPluginDbTestCaseMixin):

    resource_prefix_map = dict(
        (k.replace('_', '-'), "/metering")
        for k in metering_apidef.RESOURCE_ATTRIBUTE_MAP.keys()
    )

    def setUp(self, plugin_str=None, service_plugins=None, scheduler=None):
        if not plugin_str:
            plugin_str = ('neutron.tests.unit.extensions.test_l3.'
                          'TestL3NatIntAgentSchedulingPlugin')

        if not service_plugins:
            service_plugins = {'metering_plugin_name':
                               METERING_SERVICE_PLUGIN_KLASS}

        if not scheduler:
            scheduler = plugin_str

        ext_mgr = MeteringTestExtensionManager()
        super(TestMeteringPluginL3AgentScheduler,
              self).setUp(plugin=plugin_str, ext_mgr=ext_mgr,
                          service_plugins=service_plugins)

        self.uuid = '654f6b9d-0f36-4ae5-bd1b-01616794ca60'

        uuid = 'oslo_utils.uuidutils.generate_uuid'
        self.uuid_patch = mock.patch(uuid, return_value=self.uuid)
        self.mock_uuid = self.uuid_patch.start()

        self.tenant_id = 'a7e61382-47b8-4d40-bae3-f95981b5637b'
        self.ctx = FakeContext('', self.tenant_id, is_admin=True)
        self.context_patch = mock.patch('neutron_lib.context.Context',
                                        return_value=self.ctx)
        self.mock_context = self.context_patch.start()

        self.l3routers_patch = mock.patch(scheduler +
                                          '.get_l3_agents_hosting_routers')
        self.l3routers_mock = self.l3routers_patch.start()

        self.topic = 'metering_agent'

        add = ('neutron.api.rpc.agentnotifiers.' +
               'metering_rpc_agent_api.MeteringAgentNotifyAPI' +
               '.add_metering_label')
        self.add_patch = mock.patch(add)
        self.mock_add = self.add_patch.start()

        remove = ('neutron.api.rpc.agentnotifiers.' +
                  'metering_rpc_agent_api.MeteringAgentNotifyAPI' +
                  '.remove_metering_label')
        self.remove_patch = mock.patch(remove)
        self.mock_remove = self.remove_patch.start()

    @mock.patch('neutron.db.l3_db.L3_NAT_dbonly_mixin.create_floatingip')
    def test_add_metering_label_rpc_call(self, fip):
        second_uuid = 'e27fe2df-376e-4ac7-ae13-92f050a21f84'
        fip.return_value = FAKE_FIP_INFO
        floatingip = fip.return_value
        floatingip_id = floatingip['id']
        self.admin_context = mock.Mock()
        expected = {'metering_label': {
            'description': 'desc',
            'floatingip_id': floatingip_id,
            'id': self.uuid,
            'name': 'label',
            'project_id': self.tenant_id,
            'router_id': '',
            'shared': False,
            'tenant_id': self.tenant_id,
            'triggeredtype': ''}}

        # bind each router to a specific agent
        agent1 = agent_obj.Agent(mock.ANY, host='agent1')
        agent2 = agent_obj.Agent(mock.ANY, host='agent2')

        agents = {self.uuid: agent1,
                  second_uuid: agent2}

        def side_effect(context, routers, admin_state_up, active):
            return [agents[routers[0]]]
            self.l3routers_mock.side_effect = side_effect

            with self.router(name='router1', tenant_id=self.tenant_id,
                             set_context=True):
                self.mock_uuid.return_value = second_uuid
                with self.router(name='router2', tenant_id=self.tenant_id,
                                 set_context=True) as router:
                    router_id = router['router']['id']
                    expected['metering_label']['router_id'] = router_id
                    with self.metering_label(tenant_id=self.tenant_id,
                                             router_id=router_id,
                                             floatingip_id=floatingip_id,
                                             set_context=True):
                        self.mock_add.assert_called_with(
                            self.ctx, tools.UnorderedList(expected))

            with self.metering_label(tenant_id=self.tenant_id,
                                     router_id=router_id,
                                     floatingip_id=floatingip_id,
                                     set_context=True) as metering_label:
                self.assertEqual(metering_label, expected)


class TestMeteringPluginL3AgentSchedulerServicePlugin(
        TestMeteringPluginL3AgentScheduler):

    """Unit tests for the case where separate service plugin
    implements L3 routing.
    """

    def setUp(self):
        l3_plugin = ('neutron.tests.unit.extensions.test_l3.'
                     'TestL3NatAgentSchedulingServicePlugin')
        service_plugins = {'metering_plugin_name':
                           METERING_SERVICE_PLUGIN_KLASS,
                           'l3_plugin_name': l3_plugin}

        plugin_str = ('neutron.tests.unit.extensions.test_l3.'
                      'TestNoL3NatPlugin')

        super(TestMeteringPluginL3AgentSchedulerServicePlugin, self).setUp(
            plugin_str=plugin_str, service_plugins=service_plugins,
            scheduler=l3_plugin)


class TestMeteringPluginRpcFromL3Agent(
        test_db_base_plugin_v2.NeutronDbPluginV2TestCase,
        test_l3.L3NatTestCaseMixin,
        test_metering_db.MeteringPluginDbTestCaseMixin):

    resource_prefix_map = dict(
        (k.replace('_', '-'), "/metering")
        for k in metering_apidef.RESOURCE_ATTRIBUTE_MAP
    )

    def setUp(self):
        service_plugins = {'metering_plugin_name':
                           METERING_SERVICE_PLUGIN_KLASS}

        plugin = ('neutron.tests.unit.extensions.test_l3.'
                  'TestL3NatIntAgentSchedulingPlugin')

        ext_mgr = MeteringTestExtensionManager()
        super(TestMeteringPluginRpcFromL3Agent,
              self).setUp(plugin=plugin, service_plugins=service_plugins,
                          ext_mgr=ext_mgr)

        self.meter_plugin = directory.get_plugin(constants.METERING)

        self.tenant_id = 'admin_tenant_id'
        self.tenant_id_1 = 'tenant_id_1'
        self.tenant_id_2 = 'tenant_id_2'

        self.adminContext = context.get_admin_context()
        helpers.register_l3_agent(host='agent1')

    @mock.patch('neutron.db.l3_db.L3_NAT_dbonly_mixin.create_floatingip')
    def test_get_sync_data_metering(self, fip):
        fip.return_value = FAKE_FIP_INFO
        floatingip = fip.return_value
        floatingip_id = floatingip['id']
        with self.subnet() as subnet:
            s = subnet['subnet']
            self._set_net_external(s['network_id'])
            with self.router(name='router1', subnet=subnet) as router:
                r = router['router']
                self._add_external_gateway_to_router(r['id'], s['network_id'])
                with self.metering_label(tenant_id=r['tenant_id'],
                                         router_id=r['id'],
                                         floatingip_id=floatingip_id):
                    callbacks = metering_rpc.MeteringRpcCallbacks(
                        self.meter_plugin)
                    data = callbacks.get_sync_data_metering(self.adminContext,
                                                            host='agent1')
                    self.assertEqual([], data)

                    helpers.register_l3_agent(host='agent2')
                    data = callbacks.get_sync_data_metering(self.adminContext,
                                                            host='agent2')
                    self.assertFalse(data)

                self._remove_external_gateway_from_router(
                    r['id'], s['network_id'])

    @mock.patch('neutron.db.l3_db.L3_NAT_dbonly_mixin.create_floatingip')
    def test_get_sync_data_metering_shared(self, fip):
        fip.return_value = FAKE_FIP_INFO
        floatingip = fip.return_value
        floatingip_id = floatingip['id']
        with self.router(name='router1',
                         tenant_id=self.tenant_id_1) as router1:
            with self.router(name='router2',
                             tenant_id=self.tenant_id_2) as router:
                r = router['router']
                with self.metering_label(tenant_id=self.tenant_id,
                                         router_id=r['id'],
                                         floatingip_id=floatingip_id,
                                         shared=True):
                    callbacks = metering_rpc.MeteringRpcCallbacks(
                        self.meter_plugin)
                    callbacks.get_sync_data_metering(self.adminContext)

                    routers = [router1['router']['name'],
                               router['router']['name']]

                    self.assertIn('router1', routers)
                    self.assertIn('router2', routers)

    @mock.patch('neutron.db.l3_db.L3_NAT_dbonly_mixin.create_floatingip')
    def test_get_sync_data_metering_not_shared(self, fip):
        fip.return_value = FAKE_FIP_INFO
        floatingip = fip.return_value
        floatingip_id = floatingip['id']
        with self.router(name='router1', tenant_id=self.tenant_id_1):
            with self.router(name='router2',
                             tenant_id=self.tenant_id_2) as router:
                r = router['router']
                with self.metering_label(router_id=r['id'],
                                         floatingip_id=floatingip_id,
                                         tenant_id=self.tenant_id):
                    callbacks = metering_rpc.MeteringRpcCallbacks(
                        self.meter_plugin)
                    data = callbacks.get_sync_data_metering(self.adminContext)

                    self.assertIsNone(data)

    @mock.patch('neutron.db.l3_db.L3_NAT_dbonly_mixin.create_floatingip')
    def test_get_sync_data_metering_with_unscheduled_router(self, fip):
        fip.return_value = FAKE_FIP_INFO
        floatingip = fip.return_value
        floatingip_id = floatingip['id']
        with self.subnet() as subnet:
            s = subnet['subnet']
            self._set_net_external(s['network_id'])
            with self.router(
                name='router1', tenant_id=self.tenant_id
            ) as router1:
                self._add_external_gateway_to_router(
                    router1['router']['id'], s['network_id'])
                with self.router(name='router2',
                                 tenant_id=self.tenant_id)as router:
                    r = router['router']
                    with self.metering_label(router_id=r['id'],
                                             floatingip_id=floatingip_id,
                                             tenant_id=self.tenant_id):
                        callbacks = metering_rpc.MeteringRpcCallbacks(
                            self.meter_plugin)
                        data = callbacks.get_sync_data_metering(
                            self.adminContext, host='agent1')
                        self.assertEqual([], data)

                self._remove_external_gateway_from_router(
                    router1['router']['id'], s['network_id'])

    @mock.patch('neutron.db.l3_db.L3_NAT_dbonly_mixin.create_floatingip')
    def test_get_sync_data_metering_with_inactive_router(self, fip):
        fip.return_value = FAKE_FIP_INFO
        floatingip = fip.return_value
        floatingip_id = floatingip['id']
        with self.subnet() as subnet:
            s = subnet['subnet']
            self._set_net_external(s['network_id'])
            with self.router(
                name='router1', tenant_id=self.tenant_id
            ) as router1:
                self._add_external_gateway_to_router(
                    router1['router']['id'], s['network_id'])
                with self.router(
                    name='router2', tenant_id=self.tenant_id,
                    admin_state_up=False
                ) as router2:
                    self._add_external_gateway_to_router(
                        router2['router']['id'], s['network_id'])
                    with self.metering_label(router_id=router2['router']['id'],
                                             floatingip_id=floatingip_id,
                                             tenant_id=self.tenant_id):
                        callbacks = metering_rpc.MeteringRpcCallbacks(
                            self.meter_plugin)
                        data = callbacks.get_sync_data_metering(
                            self.adminContext, host='agent1')
                        self.assertEqual([], data)

                    self._remove_external_gateway_from_router(
                        router2['router']['id'], s['network_id'])

                self._remove_external_gateway_from_router(
                    router1['router']['id'], s['network_id'])


class TestMeteringLabelPlugin(test_l3.TestL3NatAgentSchedulingServicePlugin,
                              l3_gwmode_db.L3_NAT_db_mixin):
    supported_extension_aliases = ["router", "metering", "port_forwarding",
                                   "elastic_snat"]


class TestUpdateFIPNotifyMetering(
        test_db_base_plugin_v2.NeutronDbPluginV2TestCase,
        test_l3.L3NatTestCaseMixin):

    def setUp(self):
        svc_plugins = (
            'neutron.tests.unit.services.metering.test_metering_plugin.'
            'TestMeteringLabelPlugin',
            'neutron.services.metering.metering_plugin.MeteringPlugin',
            'neutron.services.flavors.flavors_plugin.FlavorsPlugin',
            'neutron.services.portforwarding.pf_plugin.PortForwardingPlugin',
            'neutron.services.elastic_snat.plugin.ElasticSnatPlugin')
        ext_mgr = MeteringTestExtensionManager()
        plugin = 'neutron.plugins.ml2.plugin.Ml2Plugin'
        super(TestUpdateFIPNotifyMetering, self).setUp(
            plugin=plugin, ext_mgr=ext_mgr, service_plugins=svc_plugins)
        self.l3_plugin = directory.get_plugin(constants.L3)
        self.m_plugin = directory.get_plugin(constants.METERING)
        self.pf_plugin = directory.get_plugin(constants.PORTFORWARDING)
        self.snat_plugin = directory.get_plugin(api_def.ELASTIC_SNAT)
        self.adminContext = context.get_admin_context()

    def test_associate_eip_notify_metering(self):
        with mock.patch.object(self.m_plugin,
                               'create_metering_label') as mock_call_plugin:
            with self.port() as p:
                private_sub = {
                    'subnet':
                        {'id': p['port']['fixed_ips'][0]['subnet_id']}}
                with self.floatingip_no_assoc(private_sub) as fip:
                    port_id = p['port']['id']
                    ip_address = p['port']['fixed_ips'][0]['ip_address']
                    body = self._update('floatingips', fip['floatingip']['id'],
                                        {'floatingip': {'port_id': port_id}})
                    router_id = body['floatingip']['router_id']
                    fip_id = body['floatingip']['id']
                    self.assertEqual(port_id, body['floatingip']['port_id'])
                    self.assertEqual(ip_address,
                                     body['floatingip']['fixed_ip_address'])
                    expected_meter = {
                        'metering_label': {
                            'router_id': router_id,
                            'ipversion': 'ipv4',
                            'shared': False,
                            'floatingip_id': fip_id,
                            'tenant_id': fip['floatingip']['tenant_id'],
                            'name': fip['floatingip']['floating_ip_address'],
                            'type': 'eip',
                            'description': ip_address,
                            'triggeredtype': 'service'}}
                    mock_call_plugin.assert_called_once_with(
                        mock.ANY, expected_meter)

    def test_disassociate_eip_notify_metering(self):
        with mock.patch.object(
                self.m_plugin,
                '_delete_fip_meter') as mock_call_plugin:

            with self.port() as p:
                private_sub = {
                    'subnet': {'id': p['port']['fixed_ips'][0]['subnet_id']}}
                with self.floatingip_no_assoc(private_sub) as fip:
                    port_id = p['port']['id']
                    body = self._update('floatingips',
                                        fip['floatingip']['id'],
                                        {'floatingip': {'port_id': port_id}})
                    self.assertEqual(port_id, body['floatingip']['port_id'])
                    body_update = self._update(
                        'floatingips', fip['floatingip']['id'],
                        {'floatingip': {'port_id': None}})
                    self.assertIsNone(body_update['floatingip']['port_id'])
                    self.assertIsNone(body_update['floatingip']['router_id'])
                    mock_call_plugin.assert_called_once_with(
                        mock.ANY, fip['floatingip']['id'])

    def _create_ecs_ipv6_port(self):
        with self.network(name='net1') as network, self.router() as router:
            with self.subnet(network=network,
                             cidr='2202:db8:cafe:22::/64', ip_version=6,
                             ipv6_address_mode=lib_const.DHCPV6_STATEFUL,
                             ipv6_ra_mode=lib_const.DHCPV6_STATEFUL) \
                    as subnet:
                self._router_interface_action('add',
                                              router['router']['id'],
                                              subnet['subnet']['id'], None)
                device_owner = '%s%s' % (
                    lib_const.DEVICE_OWNER_COMPUTE_PREFIX, 'nova')
                with self.port(subnet=subnet,
                               device_owner=device_owner) as port:
                    return port

    def test_create_ecs_ipv6_notify_meter(self):
        with mock.patch.object(
                self.m_plugin, 'create_metering_label') as mock_ecs_ipv6_meter:
            port = self._create_ecs_ipv6_port()
            fip_obj = l3_obj.FloatingIP.get_objects(
                self.adminContext,
                floating_port_id=port['port']['id'])[0]

            data = {'floatingip': {'admin_state_up': True}}
            req = self.new_update_request(
                'floatingips', data, fip_obj['id'], self.fmt)
            req.get_response(self.ext_api)
            res_data = jsonutils.loads(req.body)
            self.assertEqual(data, res_data)
            expected_meter = {
                'metering_label': {
                    'router_id': fip_obj.router_id,
                    'ipversion': 'ipv6',
                    'shared': False,
                    'floatingip_id': fip_obj.id,
                    'tenant_id': fip_obj.tenant_id,
                    'name': str(fip_obj.floating_ip_address),
                    'type': 'ecs_ipv6',
                    'description':
                        port['port']['fixed_ips'][0]['ip_address'],
                    'triggeredtype': 'service'}}
            mock_ecs_ipv6_meter.assert_called_once_with(
                mock.ANY, expected_meter)

    def test_admin_state_down_ecs_ipv6_notify_meter(self):
        with mock.patch.object(self.m_plugin, 'create_metering_label'
                               ) as mock_add_meter,\
                mock.patch.object(self.m_plugin, '_delete_fip_meter'
                                  ) as mock_del_meter:
            port = self._create_ecs_ipv6_port()
            fip_obj = l3_obj.FloatingIP.get_objects(
                self.adminContext,
                floating_port_id=port['port']['id'])[0]
            fip_id = fip_obj['id']
            data = {'floatingip': {'admin_state_up': True}}
            self._update('floatingips', fip_id, data)
            expected_meter = {
                'metering_label': {
                    'router_id': fip_obj.router_id,
                    'ipversion': 'ipv6',
                    'shared': False,
                    'floatingip_id': fip_obj.id,
                    'tenant_id': fip_obj.tenant_id,
                    'name': str(fip_obj.floating_ip_address),
                    'type': 'ecs_ipv6',
                    'description':
                        port['port']['fixed_ips'][0]['ip_address'],
                    'triggeredtype': 'service'}}
            mock_add_meter.assert_called_once_with(
                mock.ANY, expected_meter)
            data2 = {'floatingip': {'admin_state_up': False}}
            self._update('floatingips', fip_id, data2)
            mock_del_meter.assert_called_once_with(
                mock.ANY, fip_id)

    def test_delete_ecs_ipv6_notify_meter(self):
        with mock.patch.object(self.m_plugin, 'create_metering_label'
                               ) as mock_add_meter, \
                mock.patch.object(self.m_plugin, '_delete_fip_meter'
                                  ) as mock_del_meter:
            port = self._create_ecs_ipv6_port()
            port_id = port['port']['id']
            fip_obj = l3_obj.FloatingIP.get_objects(
                self.adminContext, floating_port_id=port_id)[0]
            self._update('floatingips', fip_obj['id'],
                         {'floatingip': {'admin_state_up': True}})
            expected_meter = {
                'metering_label': {
                    'router_id': fip_obj.router_id,
                    'ipversion': 'ipv6',
                    'shared': False,
                    'floatingip_id': fip_obj.id,
                    'tenant_id': fip_obj.tenant_id,
                    'name': str(fip_obj.floating_ip_address),
                    'type': 'ecs_ipv6',
                    'description':
                        port['port']['fixed_ips'][0]['ip_address'],
                    'triggeredtype': 'service'}}
            mock_add_meter.assert_called_once_with(
                mock.ANY, expected_meter)
            req = self.new_delete_request('ports', port_id)
            res = req.get_response(self.api)
            self.assertEqual(webob.exc.HTTPNoContent.code, res.status_int)
            mock_del_meter.assert_called_once_with(
                mock.ANY, fip_obj.id)

    def _create_pf_eip(self):
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as ext, self.network() as net1:
            with self.subnet(network=ext, cidr='**********/24'), \
                 self.subnet(network=net1, cidr='**********/24') as sub1, \
                    self.router(admin_state_up=True) as router:
                router_id = router['router']['id']
                self._add_external_gateway_to_router(router_id,
                                                     ext['network']['id'])
                self._router_interface_action('add', router_id,
                                              sub1['subnet']['id'], None)
                device_owner = '%s%s' % (
                    lib_const.DEVICE_OWNER_COMPUTE_PREFIX, 'nova')
                with self.port(subnet=sub1, device_owner=device_owner) as port:
                    ip_addr = port['port']['fixed_ips'][0]['ip_address']
                    fip = self._make_floatingip(self.fmt, ext['network']['id'])
                    fip_id = fip['floatingip']['id']
                    fip_address = fip['floatingip']['floating_ip_address']
                    pf_date = {
                        'port_forwarding':
                            {'port_forwarding': {
                                'internal_ip_address': ip_addr,
                                'internal_port_id': port['port']['id'],
                                'internal_port': 100,
                                'external_port': 100,
                                'protocol': 'tcp',
                                'floatingip_id': fip_id}},
                        'floatingip_id': fip_id}
                    return pf_date, fip_id, fip_address, router_id

    def test_create_pf_eip_notify_metering(self):
        pf_date, fip_id, fip_address, router_id = self._create_pf_eip()
        pf = self.pf_plugin.create_floatingip_port_forwarding(
            self.adminContext, **pf_date)
        self.assertEqual(fip_id, pf['floatingip_id'])
        self.assertEqual(router_id, pf['router_id'])
        self.assertIsNotNone(pf['id'])
        meter = metering.MeteringLabel.get_objects(
            self.adminContext, floatingip_id=fip_id)[0]
        self.assertEqual(fip_id, meter.floatingip_id)
        self.assertEqual(router_id, meter.router_id)
        self.assertEqual('pf_eip', meter.type)
        self.assertEqual(fip_address, meter.name)

    def test_del_pf_eip_notify_metering(self):
        # Add meter when create port_forwarding of floatingip.
        pf_date, fip_id, fip_address, router_id = self._create_pf_eip()
        pf = self.pf_plugin.create_floatingip_port_forwarding(
            self.adminContext, **pf_date)
        pf_id = pf['id']
        self.assertEqual(fip_id, pf['floatingip_id'])
        self.assertEqual(router_id, pf['router_id'])
        self.assertIsNotNone(pf['id'])
        meter = metering.MeteringLabel.get_objects(
            self.adminContext, floatingip_id=fip_id)[0]
        self.assertEqual(fip_id, meter.floatingip_id)
        self.assertEqual(router_id, meter.router_id)
        self.assertEqual('pf_eip', meter.type)
        self.assertEqual(fip_address, meter.name)

        # Delete meter when delete port forwarding of the sam floatingip.
        self.pf_plugin.delete_floatingip_port_forwarding(
            self.adminContext, pf_id, fip_id)
        pf_db = pf_obj.PortForwarding.get_object(self.adminContext, id=pf_id)
        self.assertIsNone(pf_db)
        meter2 = metering.MeteringLabel.get_objects(
            self.adminContext, floatingip_id=fip_id)
        self.assertEqual([], meter2)

    def test_del_one_of_fip_pfs_notify_metering(self):
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as ext, self.network() as net1:
            with self.subnet(network=ext, cidr='**********/24'), \
                 self.subnet(network=net1, cidr='**********/24') as sub1, \
                    self.router(admin_state_up=True) as router:
                router_id = router['router']['id']
                self._add_external_gateway_to_router(router_id,
                                                     ext['network']['id'])
                self._router_interface_action('add', router_id,
                                              sub1['subnet']['id'], None)
                device_owner = '%s%s' % (
                    lib_const.DEVICE_OWNER_COMPUTE_PREFIX, 'nova')
                with self.port(subnet=sub1, device_owner=device_owner
                               ) as p1, self.port(
                    subnet=sub1, device_owner=device_owner) as p2:
                    ip_addr = p1['port']['fixed_ips'][0]['ip_address']
                    ip_addr2 = p2['port']['fixed_ips'][0]['ip_address']
                    fip = self._make_floatingip(self.fmt, ext['network']['id'])
                    fip_id = fip['floatingip']['id']
                    fip_address = fip['floatingip']['floating_ip_address']
                    pf_date = {
                        'port_forwarding':
                            {'port_forwarding': {
                                'internal_ip_address': ip_addr,
                                'internal_port_id': p1['port']['id'],
                                'internal_port': 100,
                                'external_port': 100,
                                'protocol': 'tcp',
                                'floatingip_id': fip_id}},
                        'floatingip_id': fip_id}
                    pf = self.pf_plugin.create_floatingip_port_forwarding(
                        self.adminContext, **pf_date)
                    pf_id = pf['id']
                    self.assertEqual(fip_id, pf['floatingip_id'])
                    self.assertEqual(router_id, pf['router_id'])
                    self.assertIsNotNone(pf['id'])
                    meter = metering.MeteringLabel.get_objects(
                        self.adminContext, floatingip_id=fip_id)[0]
                    self.assertEqual(fip_id, meter.floatingip_id)
                    self.assertEqual(router_id, meter.router_id)
                    self.assertEqual('pf_eip', meter.type)
                    self.assertEqual(fip_address, meter.name)
                    rules = metering.MeteringLabelRule.get_objects(
                        self.adminContext, metering_label_id=meter.id)
                    self.assertEqual(2, len(rules))

                    pf_date2 = {
                        'port_forwarding':
                            {'port_forwarding': {
                                'internal_ip_address': ip_addr2,
                                'internal_port_id': p2['port']['id'],
                                'internal_port': 200,
                                'external_port': 200,
                                'protocol': 'tcp',
                                'floatingip_id': fip_id}},
                        'floatingip_id': fip_id}
                    pf2 = self.pf_plugin.create_floatingip_port_forwarding(
                        self.adminContext, **pf_date2)
                    self.assertEqual(fip_id, pf2['floatingip_id'])
                    self.assertEqual(router_id, pf2['router_id'])
                    self.assertIsNotNone(pf2['id'])
                    meter = metering.MeteringLabel.get_objects(
                        self.adminContext, floatingip_id=fip_id)[0]
                    self.assertEqual(fip_id, meter.floatingip_id)
                    self.assertEqual(router_id, meter.router_id)
                    self.assertEqual('pf_eip', meter.type)
                    self.assertEqual(fip_address, meter.name)
                    meter_rules = metering.MeteringLabelRule.get_objects(
                        self.adminContext, metering_label_id=meter.id)
                    self.assertEqual(4, len(meter_rules))

                    self.pf_plugin.delete_floatingip_port_forwarding(
                        self.adminContext, pf_id, fip_id)
                    pf_db = pf_obj.PortForwarding.get_object(self.adminContext,
                                                             id=pf_id)
                    self.assertIsNone(pf_db)
                    now_meter_rules = metering.MeteringLabelRule.get_objects(
                        self.adminContext, metering_label_id=meter.id)
                    self.assertEqual(2, len(now_meter_rules))

    def test_add_new_port_to_exist_pf_eip_notify_metering(self):
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as ext, self.network() as net1:
            with self.subnet(network=ext, cidr='**********/24'), \
                 self.subnet(network=net1, cidr='**********/24') as sub1, \
                    self.router(admin_state_up=True) as router:
                router_id = router['router']['id']
                self._add_external_gateway_to_router(router_id,
                                                     ext['network']['id'])
                self._router_interface_action('add', router_id,
                                              sub1['subnet']['id'], None)
                device_owner = '%s%s' % (
                    lib_const.DEVICE_OWNER_COMPUTE_PREFIX, 'nova')
                with self.port(subnet=sub1, device_owner=device_owner
                               ) as p1, self.port(
                    subnet=sub1, device_owner=device_owner) as p2:
                    ip_addr = p1['port']['fixed_ips'][0]['ip_address']
                    ip_addr2 = p2['port']['fixed_ips'][0]['ip_address']
                    fip = self._make_floatingip(self.fmt, ext['network']['id'])
                    fip_id = fip['floatingip']['id']
                    fip_address = fip['floatingip']['floating_ip_address']
                    pf_date = {
                        'port_forwarding':
                            {'port_forwarding': {
                                'internal_ip_address': ip_addr,
                                'internal_port_id': p1['port']['id'],
                                'internal_port': 100,
                                'external_port': 100,
                                'protocol': 'tcp',
                                'floatingip_id': fip_id}},
                        'floatingip_id': fip_id}
                    pf = self.pf_plugin.create_floatingip_port_forwarding(
                        self.adminContext, **pf_date)
                    self.assertEqual(fip_id, pf['floatingip_id'])
                    self.assertEqual(router_id, pf['router_id'])
                    self.assertIsNotNone(pf['id'])
                    meter = metering.MeteringLabel.get_objects(
                        self.adminContext, floatingip_id=fip_id)[0]
                    self.assertEqual(fip_id, meter.floatingip_id)
                    self.assertEqual(router_id, meter.router_id)
                    self.assertEqual('pf_eip', meter.type)
                    self.assertEqual(fip_address, meter.name)
                    rules = metering.MeteringLabelRule.get_objects(
                        self.adminContext, metering_label_id=meter.id)
                    self.assertEqual(2, len(rules))
                    # Add another port forwarding to the same fip.
                    pf_date2 = {
                        'port_forwarding':
                            {'port_forwarding': {
                                'internal_ip_address': ip_addr2,
                                'internal_port_id': p2['port']['id'],
                                'internal_port': 200,
                                'external_port': 200,
                                'protocol': 'tcp',
                                'floatingip_id': fip_id}},
                        'floatingip_id': fip_id}
                    pf2 = self.pf_plugin.create_floatingip_port_forwarding(
                        self.adminContext, **pf_date2)
                    self.assertEqual(fip_id, pf2['floatingip_id'])
                    self.assertEqual(router_id, pf2['router_id'])
                    self.assertIsNotNone(pf2['id'])
                    meter = metering.MeteringLabel.get_objects(
                        self.adminContext, floatingip_id=fip_id)[0]
                    self.assertEqual(fip_id, meter.floatingip_id)
                    self.assertEqual(router_id, meter.router_id)
                    self.assertEqual('pf_eip', meter.type)
                    self.assertEqual(fip_address, meter.name)
                    meter_rules = metering.MeteringLabelRule.get_objects(
                        self.adminContext, metering_label_id=meter.id)
                    self.assertEqual(4, len(meter_rules))

    def test_add_same_port_to_exist_pf_eip_notify_metering(self):
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as ext, self.network() as net1:
            with self.subnet(network=ext, cidr='**********/24'), \
                 self.subnet(network=net1, cidr='**********/24') as sub1, \
                    self.router(admin_state_up=True) as router:
                router_id = router['router']['id']
                self._add_external_gateway_to_router(router_id,
                                                     ext['network']['id'])
                self._router_interface_action('add', router_id,
                                              sub1['subnet']['id'], None)
                device_owner = '%s%s' % (
                    lib_const.DEVICE_OWNER_COMPUTE_PREFIX, 'nova')
                with self.port(subnet=sub1, device_owner=device_owner
                               ) as p1:
                    ip_addr = p1['port']['fixed_ips'][0]['ip_address']
                    fip = self._make_floatingip(self.fmt, ext['network']['id'])
                    fip_id = fip['floatingip']['id']
                    fip_address = fip['floatingip']['floating_ip_address']
                    pf_date = {
                        'port_forwarding':
                            {'port_forwarding': {
                                'internal_ip_address': ip_addr,
                                'internal_port_id': p1['port']['id'],
                                'internal_port': 100,
                                'external_port': 100,
                                'protocol': 'tcp',
                                'floatingip_id': fip_id}},
                        'floatingip_id': fip_id}
                    pf = self.pf_plugin.create_floatingip_port_forwarding(
                        self.adminContext, **pf_date)
                    self.assertEqual(fip_id, pf['floatingip_id'])
                    self.assertEqual(router_id, pf['router_id'])
                    self.assertIsNotNone(pf['id'])
                    meter = metering.MeteringLabel.get_objects(
                        self.adminContext, floatingip_id=fip_id)[0]
                    self.assertEqual(fip_id, meter.floatingip_id)
                    self.assertEqual(router_id, meter.router_id)
                    self.assertEqual('pf_eip', meter.type)
                    self.assertEqual(fip_address, meter.name)
                    rules = metering.MeteringLabelRule.get_objects(
                        self.adminContext, metering_label_id=meter.id)
                    self.assertEqual(2, len(rules))

                    pf_date2 = {
                        'port_forwarding':
                            {'port_forwarding': {
                                'internal_ip_address': ip_addr,
                                'internal_port_id': p1['port']['id'],
                                'internal_port': 200,
                                'external_port': 200,
                                'protocol': 'tcp',
                                'floatingip_id': fip_id}},
                        'floatingip_id': fip_id}
                    pf2 = self.pf_plugin.create_floatingip_port_forwarding(
                        self.adminContext, **pf_date2)
                    self.assertEqual(fip_id, pf2['floatingip_id'])
                    self.assertEqual(router_id, pf2['router_id'])
                    self.assertIsNotNone(pf2['id'])
                    meter = metering.MeteringLabel.get_objects(
                        self.adminContext, floatingip_id=fip_id)[0]
                    self.assertEqual(fip_id, meter.floatingip_id)
                    self.assertEqual(router_id, meter.router_id)
                    self.assertEqual('pf_eip', meter.type)
                    self.assertEqual(fip_address, meter.name)
                    meter_rules = metering.MeteringLabelRule.get_objects(
                        self.adminContext, metering_label_id=meter.id)
                    self.assertEqual(4, len(meter_rules))

    def _create_pf_range_eip(self):
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as ext, self.network() as net1:
            with self.subnet(network=ext, cidr='**********/24'), \
                 self.subnet(network=net1, cidr='**********/24') as sub1, \
                    self.router(admin_state_up=True) as router:
                router_id = router['router']['id']
                self._add_external_gateway_to_router(router_id,
                                                     ext['network']['id'])
                self._router_interface_action('add', router_id,
                                              sub1['subnet']['id'], None)
                device_owner = '%s%s' % (
                    lib_const.DEVICE_OWNER_COMPUTE_PREFIX, 'nova')
                with self.port(subnet=sub1, device_owner=device_owner) as port:
                    ip_addr = port['port']['fixed_ips'][0]['ip_address']
                    fip = self._make_floatingip(self.fmt, ext['network']['id'])
                    fip_id = fip['floatingip']['id']
                    fip_address = fip['floatingip']['floating_ip_address']
                    pf_date = {
                        'port_forwarding':
                            {'port_forwarding': {
                                'internal_port': None,
                                'external_port': None,
                                'internal_ip_address': ip_addr,
                                'internal_port_id': port['port']['id'],
                                'internal_port_range': "100:120",
                                'external_port_range': "230:250",
                                'protocol': 'tcp',
                                'floatingip_id': fip_id}},
                        'floatingip_id': fip_id}
                    return pf_date, fip_id, fip_address, router_id

    def test_create_pf_range_eip_notify_metering(self):
        pf_date, fip_id, fip_address, router_id = self._create_pf_range_eip()
        pf_info = pf_date['port_forwarding']['port_forwarding']
        ip_addr = pf_info['internal_ip_address']
        proc = pf_info['protocol']
        inter_port_range = pf_info['internal_port_range']
        pf = self.pf_plugin.create_floatingip_port_forwarding(
            self.adminContext, **pf_date)
        self.assertEqual(fip_id, pf['floatingip_id'])
        self.assertEqual(router_id, pf['router_id'])
        self.assertIsNotNone(pf['id'])
        meter = metering.MeteringLabel.get_objects(
            self.adminContext, floatingip_id=fip_id)[0]
        self.assertEqual(fip_id, meter.floatingip_id)
        self.assertEqual(router_id, meter.router_id)
        self.assertEqual('pf_eip', meter.type)
        self.assertEqual(fip_address, meter.name)
        self.assertEqual('%s;%s;%s' % (ip_addr, proc, inter_port_range),
                         meter.description)

    def _create_elastic_snat_subnet(self):
        kwargs = {'arg_list': (extnet_apidef.EXTERNAL,),
                  extnet_apidef.EXTERNAL: True}
        with self.network(**kwargs) as extnet, self.network() as net1:
            with self.subnet(network=extnet, cidr='**********/24'), \
                 self.subnet(network=net1, cidr='**********/24') as subnet1, \
                    self.subnet(network=net1, cidr='**********/24'
                                ) as subnet2, self.router() as router:
                fip = self._make_floatingip(self.fmt, extnet['network']['id'])

                snat_name1 = "snat1"
                snat_1 = {'name': snat_name1,
                          'floatingip_id': fip['floatingip']['id'],
                          'router_id': router['router']['id'],
                          'subnets': [subnet1['subnet']['id']]}

                self._add_external_gateway_to_router(router['router']['id'],
                                                     extnet['network']['id'],
                                                     enable_snat=False)
                self._router_interface_action('add', router['router']['id'],
                                              subnet1['subnet']['id'], None)
                self._router_interface_action('add', router['router']['id'],
                                              subnet2['subnet']['id'], None)

                esnat = self.snat_plugin.create_elastic_snat(
                    self.adminContext, {api_def.RESOURCE_NAME: snat_1})
                self.assertEqual(snat_name1, esnat['name'])
                self.assertEqual([subnet1['subnet']['id']], esnat['subnets'])
                return esnat, fip, router

    def test_create_snat_notify_metering(self):
        with mock.patch.object(self.m_plugin, 'create_snat_meter'):
            esnat, fip, router = self._create_elastic_snat_subnet()
            meter = metering.MeteringLabel.get_objects(
                self.adminContext, floatingip_id=fip['floatingip']['id'])[0]
            meter_rules = metering.MeteringLabelRule.get_objects(
                self.adminContext, metering_label_id=meter['id'])
            self.assertEqual(meter['floatingip_id'], fip['floatingip']['id'])
            self.assertEqual(meter['router_id'], router['router']['id'])
            self.assertEqual(meter['ipversion'], 'ipv4')
            self.assertEqual(meter['name'],
                             fip['floatingip']['floating_ip_address'])
            self.assertEqual(meter['type'], 'snat_eip')
            self.assertEqual(meter['description'], 'snat_meter')
            self.assertEqual(2, len(meter_rules))

    def test_update_snat_notify_metering(self):
        with mock.patch.object(self.m_plugin, 'update_snat_meter'):
            esnat, fip, router = self._create_elastic_snat_subnet()
            meter1 = metering.MeteringLabel.get_objects(
                self.adminContext, floatingip_id=fip['floatingip']['id'])[0]
            meter1_rules = metering.MeteringLabelRule.get_objects(
                self.adminContext, metering_label_id=meter1['id'])
            self.assertEqual(2, len(meter1_rules))
            # Update
            snat_name2 = "snat2"
            snat_2 = {'name': snat_name2,
                      'floatingip_id': fip['floatingip']['id'],
                      'router_id': router['router']['id'],
                      'internal_cidrs': ['**********/24', '**********/24']}
            esnat = self.snat_plugin.update_elastic_snat(
                self.adminContext, esnat['id'],
                {api_def.RESOURCE_NAME: snat_2})
            self.assertEqual(snat_name2, esnat['name'])
            self.assertEqual(['**********/24', '**********/24'],
                             esnat['internal_cidrs'])
            meter = metering.MeteringLabel.get_objects(
                self.adminContext, floatingip_id=fip['floatingip']['id'])[0]
            meter_rules = metering.MeteringLabelRule.get_objects(
                self.adminContext, metering_label_id=meter['id'])
            self.assertEqual(4, len(meter_rules))
            self.assertEqual(meter['id'], meter1['id'])

    def test_delete_snat_notify_metering(self):
        with mock.patch.object(self.m_plugin, 'delete_snat_meter'):
            esnat, fip, router = self._create_elastic_snat_subnet()
            meter = metering.MeteringLabel.get_objects(
                self.adminContext, floatingip_id=fip['floatingip']['id'])[0]
            meter_rules = metering.MeteringLabelRule.get_objects(
                self.adminContext, metering_label_id=meter['id'])
            self.assertEqual(meter['floatingip_id'], fip['floatingip']['id'])
            self.assertEqual(meter['router_id'], router['router']['id'])
            self.assertEqual(meter['ipversion'], 'ipv4')
            self.assertEqual(meter['name'],
                             fip['floatingip']['floating_ip_address'])
            self.assertEqual(meter['type'], 'snat_eip')
            self.assertEqual(meter['description'], 'snat_meter')
            self.assertEqual(2, len(meter_rules))
            # delete
            self.snat_plugin.delete_elastic_snat(self.adminContext,
                                                 esnat['id'])
            esnats = self.snat_plugin.get_elastic_snats(self.adminContext)
            self.assertEqual([], esnats)
            meters = metering.MeteringLabel.get_objects(
                self.adminContext, floatingip_id=fip['floatingip']['id'])
            self.assertEqual([], meters)
            filters = {}
            if meters:
                filters = {'metering_label_id': meter['id']}
            rules = self.m_plugin.get_metering_label_rules(self.adminContext,
                                                           filters=filters)
            self.assertEqual([], rules)
